import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Welcome from "./pages/Welcome";
import Login from "./pages/Login";
import LineCallback from "./pages/auth/LineCallback";
import ServiceSelection from "./pages/ServiceSelection";
import TimeSelection from "./pages/TimeSelection";
import BookingConfirm from "./pages/BookingConfirm";
import BookingSuccess from "./pages/BookingSuccess";
import MemberProfile from "./pages/MemberProfile";
import WalletBalance from "./pages/WalletBalance";
import BusinessSurvey from "./pages/BusinessSurvey";
import RewardsExchange from "./pages/RewardsExchange";
import PersonalSettings from "./pages/PersonalSettings";
import BookingManagement from "./pages/BookingManagement";
import CouponManagement from "./pages/CouponManagement";
import AdminDashboard from "./pages/AdminDashboard";
import LineSettings from "./pages/admin/LineSettings";
import LineLoginTest from "./pages/admin/LineLoginTest";
import MemberManagement from "./pages/admin/MemberManagement";
import TimeSlotManagement from "./pages/admin/TimeSlotManagement";
import StaffManagement from "./pages/admin/StaffManagement";
import CategoryManagement from "./pages/admin/CategoryManagement";
import ServiceManagement from "./pages/admin/ServiceManagement";
import AdminCouponManagement from "./pages/admin/AdminCouponManagement";
import DatabaseDebug from "./pages/admin/DatabaseDebug";
import PermissionFix from "./pages/admin/PermissionFix";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Welcome />} />
          <Route path="/login" element={<Login />} />
          <Route path="/auth/line/callback" element={<LineCallback />} />
          <Route path="/services" element={<ServiceSelection />} />
          <Route path="/booking-time" element={<TimeSelection />} />
          <Route path="/booking-confirm" element={<BookingConfirm />} />
          <Route path="/booking-success" element={<BookingSuccess />} />
          <Route path="/profile" element={<MemberProfile />} />
          <Route path="/wallet" element={<WalletBalance />} />
          <Route path="/business-survey" element={<BusinessSurvey />} />
          <Route path="/rewards" element={<RewardsExchange />} />
          <Route path="/settings" element={<PersonalSettings />} />
          <Route path="/booking-management" element={<BookingManagement />} />
          <Route path="/coupons" element={<CouponManagement />} />
          <Route path="/admin" element={<AdminDashboard />} />
        <Route path="/admin/line-settings" element={<LineSettings />} />
        <Route path="/admin/line-test" element={<LineLoginTest />} />
        <Route path="/admin/members" element={<MemberManagement />} />
        <Route path="/admin/time-slots" element={<TimeSlotManagement />} />
        <Route path="/admin/staff" element={<StaffManagement />} />
        <Route path="/admin/categories" element={<CategoryManagement />} />
        <Route path="/admin/services" element={<ServiceManagement />} />
        <Route path="/admin/coupons" element={<AdminCouponManagement />} />
        <Route path="/admin/database-debug" element={<DatabaseDebug />} />
        <Route path="/admin/permission-fix" element={<PermissionFix />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
