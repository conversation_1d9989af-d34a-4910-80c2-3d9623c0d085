import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  User,
  Calendar,
  Ticket,
  DollarSign,
  FileText,
  Gift,
  Settings,
  CalendarClock,
  CreditCard,
  ChevronRight,
  ArrowLeft
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

interface UserProfile {
  id: string;
  display_name: string | null;
  email: string | null;
  phone: string | null;
  avatar_url: string | null;
  created_at: string;
}

const MemberProfile = () => {
  const navigate = useNavigate();
  const { user: authUser } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [bookingCount, setBookingCount] = useState(0);
  const [loading, setLoading] = useState(true);

  // 載入用戶資料
  useEffect(() => {
    if (authUser) {
      loadUserProfile();
    }
  }, [authUser]);

  const loadUserProfile = async () => {
    if (!authUser) return;

    console.log('載入用戶資料，用戶 ID:', authUser.id);
    console.log('用戶 email:', authUser.email);

    try {
      // 載入用戶 profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', authUser.id)
        .single();

      console.log('Profile 查詢結果:', { profile, profileError });

      if (profileError) throw profileError;

      setUserProfile(profile);

      // 載入預約次數
      const { data: bookings, error: bookingError } = await supabase
        .from('bookings')
        .select('id')
        .eq('user_id', authUser.id);

      if (bookingError) throw bookingError;

      setBookingCount(bookings?.length || 0);
    } catch (error: any) {
      console.error('Error loading user profile:', error);
      toast({
        title: "載入失敗",
        description: "無法載入用戶資料",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 如果未登入，導向登入頁面
  if (!authUser) {
    navigate('/login');
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">載入中...</p>
        </div>
      </div>
    );
  }

  const menuItems = [
    {
      icon: DollarSign,
      title: "目前儲值金",
      value: 0, // TODO: 實作錢包功能
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      icon: FileText,
      title: "問券與同意書",
      value: null,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Gift,
      title: "紅利兌換",
      value: null,
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      icon: User,
      title: "個人設定",
      value: null,
      color: "text-cyan-600",
      bgColor: "bg-cyan-50"
    },
    {
      icon: CalendarClock,
      title: "預約管理",
      value: null,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: CreditCard,
      title: "票券管理",
      value: null,
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ];

  const displayName = userProfile?.display_name || userProfile?.email || '用戶';
  const avatarUrl = userProfile?.avatar_url;

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100">
      {/* 版本標識 - 用於確認新版本已部署 */}
      <div className="bg-green-500 text-white text-center py-1 text-xs">
        新版本已載入 - v2.0 - {new Date().toLocaleTimeString()}
      </div>

      {/* Header */}
      <div className="bg-gradient-to-r from-pink-400 to-rose-400 text-white p-6 pb-16 relative">
        {/* Back Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate(-1)}
          className="absolute top-4 left-4 text-white hover:bg-white/20"
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>

        {/* User Profile */}
        <div className="space-y-4 mt-8">
          <div className="relative">
            <Avatar className="w-24 h-24 mx-auto border-4 border-white shadow-lg">
              {avatarUrl && <AvatarImage src={avatarUrl} />}
              <AvatarFallback className="bg-muted text-2xl font-semibold">
                {displayName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="absolute bottom-0 right-1/2 transform translate-x-6 translate-y-1 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md">
              <User className="w-4 h-4 text-muted-foreground" />
            </div>
          </div>

          <h2 className="text-xl font-semibold text-center">{displayName}</h2>

          <Button
            className="beauty-gradient text-white px-8 py-3 rounded-full font-medium mx-auto block"
            onClick={() => navigate("/services")}
          >
            我要預約
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="p-6 text-center card-shadow">
            <div className="text-3xl font-bold text-foreground mb-1">{bookingCount}</div>
            <div className="text-sm text-muted-foreground">我的預約</div>
          </Card>
          <Card className="p-6 text-center card-shadow">
            <div className="text-3xl font-bold text-primary mb-1">0</div>
            <div className="text-sm text-muted-foreground">擁有票券</div>
          </Card>
        </div>

        {/* Menu Items */}
        <div className="space-y-3">
          {menuItems.map((item, index) => (
            <Card 
              key={index}
              className="p-4 cursor-pointer smooth-transition hover:shadow-md card-shadow"
              onClick={() => {
                const navigationMap = {
                  "目前儲值金": "/wallet",
                  "問券與同意書": "/business-survey", 
                  "紅利兌換": "/rewards",
                  "個人設定": "/settings",
                  "預約管理": "/booking-management",
                  "票券管理": "/coupons"
                };
                const path = navigationMap[item.title as keyof typeof navigationMap];
                if (path) navigate(path);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center`}>
                    <item.icon className={`w-5 h-5 ${item.color}`} />
                  </div>
                  <span className="font-medium text-foreground">{item.title}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {item.value !== null && (
                    <Badge variant="secondary" className="text-sm">
                      {item.value}
                    </Badge>
                  )}
                  <ChevronRight className="w-5 h-5 text-muted-foreground" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="text-center py-8 space-y-1">
        <p className="text-xs text-muted-foreground">Powered by 夯客</p>
        <p className="text-xs text-muted-foreground">v3.11.0</p>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border">
        <div className="grid grid-cols-2">
          <Button 
            variant="ghost" 
            className="h-16 flex flex-col space-y-1 rounded-none"
            onClick={() => navigate("/services")}
          >
            <Calendar className="w-5 h-5" />
            <span className="text-xs">我要預約</span>
          </Button>
          <Button 
            variant="ghost" 
            className="h-16 flex flex-col space-y-1 rounded-none bg-accent"
          >
            <Settings className="w-5 h-5" />
            <span className="text-xs">會員資訊</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MemberProfile;