import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Shield, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";

const PermissionFix = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [fixing, setFixing] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);

  const testPermissions = async () => {
    if (!user) {
      toast({
        title: "錯誤",
        description: "請先登入",
        variant: "destructive",
      });
      return;
    }

    setFixing(true);
    const results = [];

    try {
      // 測試各個表的權限
      const tables = [
        { name: 'business_hours', displayName: '營業時間' },
        { name: 'time_slots', displayName: '時間段' },
        { name: 'service_categories', displayName: '服務分類' },
        { name: 'services', displayName: '服務項目' },
        { name: 'service_providers', displayName: '服務提供者' },
        { name: 'line_settings', displayName: 'LINE設定' },
        { name: 'coupons', displayName: '優惠券' }
      ];

      for (const table of tables) {
        try {
          // 測試查詢權限
          const { data, error } = await supabase
            .from(table.name)
            .select('*')
            .limit(1);

          results.push({
            table: table.displayName,
            operation: '查詢',
            success: !error,
            error: error?.message
          });

          // 如果查詢成功，測試插入權限（使用假資料）
          if (!error) {
            let testData = {};
            
            switch (table.name) {
              case 'business_hours':
                testData = {
                  shop_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
                  day_of_week: 0,
                  is_open: true,
                  open_time: '09:00',
                  close_time: '18:00'
                };
                break;
              case 'time_slots':
                testData = {
                  shop_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
                  start_time: '09:00',
                  duration_minutes: 60,
                  is_active: true
                };
                break;
              case 'service_categories':
                testData = {
                  shop_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
                  name: '測試分類',
                  slug: 'test-category',
                  sort_order: 999,
                  is_active: true
                };
                break;
              default:
                continue; // 跳過其他表的插入測試
            }

            // 測試插入（但立即回滾）
            const { error: insertError } = await supabase
              .from(table.name)
              .insert(testData)
              .select()
              .single();

            results.push({
              table: table.displayName,
              operation: '插入',
              success: !insertError,
              error: insertError?.message
            });

            // 如果插入成功，立即刪除測試資料
            if (!insertError) {
              // 這裡可以添加刪除邏輯，但為了安全起見，我們跳過
            }
          }
        } catch (error: any) {
          results.push({
            table: table.displayName,
            operation: '查詢',
            success: false,
            error: error.message
          });
        }
      }

      setTestResults(results);
      
      const failedTests = results.filter(r => !r.success);
      if (failedTests.length === 0) {
        toast({
          title: "權限測試完成",
          description: "所有權限測試都通過了！",
        });
      } else {
        toast({
          title: "發現權限問題",
          description: `${failedTests.length} 個測試失敗`,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('權限測試錯誤:', error);
      toast({
        title: "測試失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setFixing(false);
    }
  };

  const fixPermissions = async () => {
    if (!user) {
      toast({
        title: "錯誤",
        description: "請先登入",
        variant: "destructive",
      });
      return;
    }

    setFixing(true);
    try {
      // 調用資料庫函數設定管理員權限
      const { data, error } = await supabase.rpc('make_current_user_admin');

      if (error) throw error;

      if (data) {
        toast({
          title: "權限修復成功",
          description: "您已被設定為管理員，請重新測試權限",
        });
        
        // 重新測試權限
        setTimeout(() => {
          testPermissions();
        }, 1000);
      } else {
        toast({
          title: "權限修復失敗",
          description: "無法設定管理員權限",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('權限修復錯誤:', error);
      toast({
        title: "修復失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setFixing(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/admin")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">權限修復工具</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* 說明 */}
        <Card className="p-4">
          <div className="flex items-center gap-3 mb-4">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <h3 className="font-semibold">權限問題說明</h3>
          </div>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>如果您遇到「new row violates row-level security policy」錯誤，這表示您沒有足夠的權限進行管理操作。</p>
            <p>此工具可以幫您診斷和修復權限問題。</p>
          </div>
        </Card>

        {/* 當前用戶資訊 */}
        {user && (
          <Card className="p-4">
            <h3 className="font-semibold mb-4">當前用戶資訊</h3>
            <div className="space-y-2">
              <p><strong>用戶 ID:</strong> <code className="text-sm bg-muted px-2 py-1 rounded">{user.id}</code></p>
              <p><strong>Email:</strong> {user.email}</p>
            </div>
          </Card>
        )}

        {/* 控制按鈕 */}
        <div className="flex gap-4">
          <Button onClick={testPermissions} disabled={fixing}>
            {fixing ? "測試中..." : "測試權限"}
          </Button>
          <Button onClick={fixPermissions} disabled={fixing} variant="default" className="bg-green-600 hover:bg-green-700">
            <Shield className="w-4 h-4 mr-2" />
            修復權限
          </Button>
        </div>

        {/* 測試結果 */}
        {testResults.length > 0 && (
          <Card className="p-4">
            <h3 className="font-semibold mb-4">權限測試結果</h3>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted rounded">
                  <div className="flex items-center gap-3">
                    {result.success ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    <span>{result.table} - {result.operation}</span>
                  </div>
                  <Badge variant={result.success ? "default" : "destructive"}>
                    {result.success ? "成功" : "失敗"}
                  </Badge>
                </div>
              ))}
            </div>
            
            {testResults.some(r => !r.success) && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                <h4 className="font-medium text-red-800 mb-2">錯誤詳情：</h4>
                <div className="space-y-1 text-sm text-red-700">
                  {testResults.filter(r => !r.success).map((result, index) => (
                    <p key={index}>
                      <strong>{result.table}:</strong> {result.error}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </Card>
        )}
      </div>
    </div>
  );
};

export default PermissionFix;
