import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Pencil, Trash2, Save, X, Clock, DollarSign } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface Service {
  id: string;
  name: string;
  description: string | null;
  category_id: string;
  duration_minutes: number;
  price_min: number;
  price_max: number | null;
  is_active: boolean;
  category?: { name: string };
}

interface Category {
  id: string;
  name: string;
}

const ServiceManagement = () => {
  const navigate = useNavigate();
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category_id: "",
    duration_minutes: 60,
    price_min: 0,
    price_max: null as number | null
  });
  const [isAdding, setIsAdding] = useState(false);
  const [loading, setLoading] = useState(true);

  const SHOP_ID = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 載入分類
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('service_categories')
        .select('id, name')
        .eq('shop_id', SHOP_ID)
        .eq('is_active', true)
        .order('sort_order');

      if (categoriesError) throw categoriesError;
      setCategories(categoriesData || []);

      // 載入服務項目
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select(`
          *,
          category:service_categories(name)
        `)
        .order('sort_order');

      if (servicesError) throw servicesError;
      setServices(servicesData || []);
    } catch (error: any) {
      toast({
        title: "載入資料失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (service: Service) => {
    setEditingId(service.id);
    setFormData({
      name: service.name,
      description: service.description || "",
      category_id: service.category_id,
      duration_minutes: service.duration_minutes,
      price_min: service.price_min,
      price_max: service.price_max
    });
  };

  const handleDelete = async (id: string) => {
    if (!confirm('確定要刪除此服務項目嗎？此操作無法復原。')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "成功",
        description: "服務項目已刪除"
      });

      await fetchData();
    } catch (error: any) {
      toast({
        title: "刪除失敗",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleToggleStatus = async (id: string) => {
    const service = services.find(s => s.id === id);
    if (!service) return;

    try {
      const { error } = await supabase
        .from('services')
        .update({ is_active: !service.is_active })
        .eq('id', id);

      if (error) throw error;

      await fetchData();
    } catch (error: any) {
      toast({
        title: "更新狀態失敗",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setFormData({
      name: "",
      description: "",
      category_id: "",
      duration_minutes: 60,
      price_min: 0,
      price_max: null
    });
  };

  const handleSave = async () => {
    if (!formData.name.trim() || !formData.category_id || formData.price_min <= 0) {
      toast({
        title: "錯誤",
        description: "請填寫所有必填欄位",
        variant: "destructive"
      });
      return;
    }

    try {
      if (isAdding) {
        const { error } = await supabase
          .from('services')
          .insert({
            category_id: formData.category_id,
            name: formData.name,
            description: formData.description || null,
            duration_minutes: formData.duration_minutes,
            price_min: formData.price_min,
            price_max: formData.price_max,
            is_active: true
          });

        if (error) throw error;
        toast({ title: "成功", description: "服務項目已新增" });
        setIsAdding(false);
      } else if (editingId) {
        const { error } = await supabase
          .from('services')
          .update({
            category_id: formData.category_id,
            name: formData.name,
            description: formData.description || null,
            duration_minutes: formData.duration_minutes,
            price_min: formData.price_min,
            price_max: formData.price_max
          })
          .eq('id', editingId);

        if (error) throw error;
        toast({ title: "成功", description: "服務項目已更新" });
        setEditingId(null);
      }

      setFormData({
        name: "",
        description: "",
        category_id: "",
        duration_minutes: 60,
        price_min: 0,
        price_max: null
      });
      await fetchData();
    } catch (error: any) {
      toast({
        title: "操作失敗",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">服務項目管理</h1>
            <p className="text-gray-600">管理所有提供的服務項目</p>
          </div>
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
        </div>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>服務項目列表</CardTitle>
            <Button onClick={() => setIsAdding(true)} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              新增服務
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">載入中...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {isAdding && (
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>服務名稱 *</Label>
                        <Input
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          placeholder="請輸入服務名稱"
                        />
                      </div>
                      <div>
                        <Label>服務分類 *</Label>
                        <Select value={formData.category_id} onValueChange={(value) => setFormData({...formData, category_id: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="選擇分類" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>{category.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>服務時長(分鐘)</Label>
                        <Input
                          type="number"
                          value={formData.duration_minutes}
                          onChange={(e) => setFormData({...formData, duration_minutes: parseInt(e.target.value) || 0})}
                          min="15"
                          step="15"
                        />
                      </div>
                      <div>
                        <Label>服務價格(元) *</Label>
                        <Input
                          type="number"
                          value={formData.price_min}
                          onChange={(e) => setFormData({...formData, price_min: parseInt(e.target.value) || 0})}
                          min="0"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <Label>服務描述</Label>
                        <Textarea
                          value={formData.description}
                          onChange={(e) => setFormData({...formData, description: e.target.value})}
                          placeholder="請輸入服務描述"
                          rows={3}
                        />
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button onClick={handleSave} className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        儲存
                      </Button>
                      <Button onClick={handleCancel} variant="outline" className="flex items-center gap-2">
                        <X className="h-4 w-4" />
                        取消
                      </Button>
                    </div>
                  </div>
                )}

                {services.map((service) => (
                  <div key={service.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    {editingId === service.id ? (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>服務名稱 *</Label>
                            <Input
                              value={formData.name}
                              onChange={(e) => setFormData({...formData, name: e.target.value})}
                              placeholder="請輸入服務名稱"
                            />
                          </div>
                          <div>
                            <Label>服務分類 *</Label>
                            <Select value={formData.category_id} onValueChange={(value) => setFormData({...formData, category_id: value})}>
                              <SelectTrigger>
                                <SelectValue placeholder="選擇分類" />
                              </SelectTrigger>
                              <SelectContent>
                                {categories.map((category) => (
                                  <SelectItem key={category.id} value={category.id}>{category.name}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>服務時長(分鐘)</Label>
                            <Input
                              type="number"
                              value={formData.duration_minutes}
                              onChange={(e) => setFormData({...formData, duration_minutes: parseInt(e.target.value) || 0})}
                              min="15"
                              step="15"
                            />
                          </div>
                          <div>
                            <Label>服務價格(元) *</Label>
                            <Input
                              type="number"
                              value={formData.price_min}
                              onChange={(e) => setFormData({...formData, price_min: parseInt(e.target.value) || 0})}
                              min="0"
                            />
                          </div>
                          <div className="md:col-span-2">
                            <Label>服務描述</Label>
                            <Textarea
                              value={formData.description}
                              onChange={(e) => setFormData({...formData, description: e.target.value})}
                              placeholder="請輸入服務描述"
                              rows={3}
                            />
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={handleSave} className="flex items-center gap-2">
                            <Save className="h-4 w-4" />
                            儲存
                          </Button>
                          <Button onClick={handleCancel} variant="outline" className="flex items-center gap-2">
                            <X className="h-4 w-4" />
                            取消
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-lg">{service.name}</h3>
                              <Badge variant="outline">{service.category?.name}</Badge>
                            </div>
                            {service.description && <p className="text-gray-600 mb-3">{service.description}</p>}
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>{service.duration_minutes} 分鐘</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <DollarSign className="h-4 w-4" />
                                <span>NT$ {service.price_min}{service.price_max ? `-${service.price_max}` : ''}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button 
                            variant={service.is_active ? "default" : "secondary"} 
                            size="sm"
                            onClick={() => handleToggleStatus(service.id)}
                          >
                            {service.is_active ? "啟用中" : "已停用"}
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEdit(service)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-red-600 hover:text-red-800"
                            onClick={() => handleDelete(service.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ServiceManagement;