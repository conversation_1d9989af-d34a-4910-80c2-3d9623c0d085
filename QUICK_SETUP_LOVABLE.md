# 🚀 Lovable 環境 LINE Login 快速設定

## 📍 重要資訊

**專案網址**: https://line-beauty-appoint.lovable.app/
**Callback URL**: https://line-beauty-appoint.lovable.app/auth/line/callback

## ⚡ 快速設定步驟

### 1. LINE Developers Console 設定

1. 前往 [LINE Developers Console](https://developers.line.biz/console/)
2. 登入您的 LINE 帳號
3. 選擇您的 Provider 或創建新的
4. 創建或選擇 LINE Login Channel
5. 在 "LINE Login settings" 中的 "Callback URL" 欄位添加：
   ```
   https://line-beauty-appoint.lovable.app/auth/line/callback
   ```
6. 複製 Channel ID 和 Channel Secret

### 2. 系統設定

1. 前往 [LINE API 設定頁面](https://line-beauty-appoint.lovable.app/admin/line-settings)
2. 填入從 LINE Developers Console 複製的：
   - Channel ID
   - Channel Secret
3. 點擊 "自動設定" 按鈕來設定正確的 Redirect URI
4. 點擊 "儲存設定"

### 3. 測試登入

1. 前往 [登入頁面](https://line-beauty-appoint.lovable.app/login)
2. 點擊 "使用 LINE 登入" 按鈕
3. 應該會正確導向到 LINE 授權頁面

## 🔍 診斷工具

如果遇到問題，可以使用：
- [診斷頁面](https://line-beauty-appoint.lovable.app/admin/line-test) - 自動檢測設定問題
- [設定頁面](https://line-beauty-appoint.lovable.app/admin/line-settings) - 測試 LINE Login 功能

## ✅ 檢查清單

- [ ] 在 LINE Developers Console 中註冊了正確的 Callback URL
- [ ] Channel ID 已填入系統設定
- [ ] Channel Secret 已填入系統設定
- [ ] Redirect URI 設定為 `https://line-beauty-appoint.lovable.app/auth/line/callback`
- [ ] LINE Login Channel 狀態為 "Published"
- [ ] 測試登入流程正常

## 🆘 常見問題

**Q: 出現 "Invalid redirect_uri" 錯誤**
A: 請確保在 LINE Developers Console 中完全匹配地註冊了 callback URL

**Q: 登入後沒有反應**
A: 檢查瀏覽器控制台是否有錯誤訊息，並使用診斷頁面檢查設定

**Q: 無法儲存設定**
A: 請確保您有管理員權限，或聯繫系統管理員

## 📞 需要協助？

1. 使用 [診斷頁面](https://line-beauty-appoint.lovable.app/admin/line-test) 查看詳細問題
2. 檢查瀏覽器控制台的錯誤訊息
3. 參考完整的 [問題排除指南](./TROUBLESHOOTING_LINE_LOGIN.md)
