import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Calendar, Clock, User, MapPin, Phone, MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const BookingManagement = () => {
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState("upcoming");

  const bookings = {
    upcoming: [
      {
        id: "HK202407150001",
        service: "美甲-單色、法式、貓眼、璀璨款",
        staff: "專業美甲師 Anna",
        date: "2024/07/20",
        time: "14:00",
        duration: "2小時",
        price: 1680,
        status: "confirmed",
        address: "台北市中山區建國北路二段123號"
      },
      {
        id: "HK202407160002",
        service: "臉部深層清潔保養",
        staff: "美容師 Jessica",
        date: "2024/07/25",
        time: "10:30",
        duration: "90分鐘",
        price: 2200,
        status: "confirmed",
        address: "台北市中山區建國北路二段123號"
      }
    ],
    completed: [
      {
        id: "HK202407100003",
        service: "美甲-光療指甲",
        staff: "專業美甲師 Anna",
        date: "2024/07/10",
        time: "15:00",
        duration: "2小時",
        price: 1500,
        status: "completed",
        address: "台北市中山區建國北路二段123號"
      }
    ],
    cancelled: []
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'confirmed':
        return { color: 'bg-green-100 text-green-800', text: '已確認' };
      case 'completed':
        return { color: 'bg-blue-100 text-blue-800', text: '已完成' };
      case 'cancelled':
        return { color: 'bg-red-100 text-red-800', text: '已取消' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: '未知' };
    }
  };

  const tabs = [
    { id: 'upcoming', label: '即將到來', count: bookings.upcoming.length },
    { id: 'completed', label: '已完成', count: bookings.completed.length },
    { id: 'cancelled', label: '已取消', count: bookings.cancelled.length }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">預約管理</h1>
          <div className="w-10"></div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-border">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      <div className="p-4 space-y-4">
        {bookings[activeTab as keyof typeof bookings].length > 0 ? (
          bookings[activeTab as keyof typeof bookings].map((booking) => {
            const statusInfo = getStatusInfo(booking.status);
            
            return (
              <Card key={booking.id} className="p-4 card-shadow">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-foreground">{booking.service}</h3>
                      <p className="text-sm text-muted-foreground">預約編號: {booking.id}</p>
                    </div>
                    <Badge className={statusInfo.color}>
                      {statusInfo.text}
                    </Badge>
                  </div>

                  {/* Staff Info */}
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-foreground">{booking.staff}</span>
                  </div>

                  {/* Date & Time */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-foreground">{booking.date}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-foreground">{booking.time} ({booking.duration})</span>
                    </div>
                  </div>

                  {/* Address */}
                  <div className="flex items-start space-x-2">
                    <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                    <span className="text-sm text-foreground">{booking.address}</span>
                  </div>

                  {/* Price */}
                  <div className="bg-primary-lighter p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">服務費用</span>
                      <span className="font-semibold text-primary">${booking.price}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-2">
                    {booking.status === 'confirmed' && (
                      <>
                        <Button variant="outline" size="sm" className="flex-1">
                          <Phone className="w-4 h-4 mr-1" />
                          聯絡店家
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">
                          <MessageCircle className="w-4 h-4 mr-1" />
                          修改預約
                        </Button>
                      </>
                    )}
                    
                    {booking.status === 'completed' && (
                      <Button size="sm" className="flex-1">
                        <MessageCircle className="w-4 h-4 mr-1" />
                        撰寫評價
                      </Button>
                    )}
                    
                    {booking.status === 'confirmed' && (
                      <Button variant="destructive" size="sm" className="flex-1">
                        取消預約
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })
        ) : (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium text-foreground mb-2">沒有{tabs.find(t => t.id === activeTab)?.label}的預約</h3>
            <p className="text-muted-foreground mb-4">
              {activeTab === 'upcoming' && '您目前沒有即將到來的預約'}
              {activeTab === 'completed' && '您還沒有完成任何預約'}
              {activeTab === 'cancelled' && '您沒有取消的預約記錄'}
            </p>
            {activeTab === 'upcoming' && (
              <Button onClick={() => navigate("/services")}>
                立即預約
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingManagement;