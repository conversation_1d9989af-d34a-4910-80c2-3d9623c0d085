import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { useLineLogin } from "@/hooks/useLineLogin";
import { LoadingScreen } from "@/components/auth/LoadingScreen";
import { ErrorScreen } from "@/components/auth/ErrorScreen";

const LineCallback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { error, loading, processLineCallback } = useLineLogin();

  // 從 URL 獲取參數用於錯誤診斷
  const params = new URLSearchParams(location.search);
  const receivedState = params.get("state");
  const savedState = localStorage.getItem("line_login_state");

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    processLineCallback(params).then(() => {
      // 成功後導向到會員管理頁面
      navigate("/profile");
    }).catch((err) => {
      console.error("LINE callback error:", err);
    });
  }, [location.search, navigate, processLineCallback]);

  if (loading) {
    return (
      <LoadingScreen
        title="處理 LINE 登入中..."
        description="請稍候，正在驗證您的 LINE 帳號"
      />
    );
  }

  if (error) {
    return (
      <ErrorScreen
        error={error}
        receivedState={receivedState}
        savedState={savedState}
        onRetry={() => {
          const params = new URLSearchParams(location.search);
          processLineCallback(params).then(() => {
            navigate("/profile");
          }).catch((err) => {
            console.error("Retry failed:", err);
          });
        }}
      />
    );
  }

  return (
    <LoadingScreen
      title="登入成功"
      description="正在為您導向..."
    />
  );
};

export default LineCallback;
