import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Settings, Users, Calendar, Tag, Ticket, List, ArrowLeft } from "lucide-react";

const AdminDashboard = () => {
  const navigate = useNavigate();

  const adminModules = [
    {
      title: "LINE API 設定",
      description: "設定 LINE Login API 與 Webhook",
      icon: Settings,
      path: "/admin/line-settings",
      color: "bg-blue-50 text-blue-600"
    },
    {
      title: "會員管理",
      description: "查看與管理會員資料",
      icon: Users,
      path: "/admin/members",
      color: "bg-green-50 text-green-600"
    },
    {
      title: "預約時間管理",
      description: "設定營業時間與可預約時段",
      icon: Calendar,
      path: "/admin/time-slots",
      color: "bg-purple-50 text-purple-600"
    },
    {
      title: "服務人員管理",
      description: "新增與編輯服務人員資料",
      icon: Users,
      path: "/admin/staff",
      color: "bg-orange-50 text-orange-600"
    },
    {
      title: "服務分類管理",
      description: "管理服務項目分類",
      icon: List,
      path: "/admin/categories",
      color: "bg-pink-50 text-pink-600"
    },
    {
      title: "服務項目管理",
      description: "新增與編輯服務項目",
      icon: Tag,
      path: "/admin/services",
      color: "bg-indigo-50 text-indigo-600"
    },
    {
      title: "票券管理",
      description: "創建與管理優惠票券",
      icon: Ticket,
      path: "/admin/coupons",
      color: "bg-yellow-50 text-yellow-600"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">管理後台</h1>
            <p className="text-gray-600">系統管理與設定中心</p>
          </div>
          <Button 
            variant="outline" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回前台
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {adminModules.map((module, index) => {
            const IconComponent = module.icon;
            return (
              <Card 
                key={index} 
                className="hover:shadow-lg transition-shadow cursor-pointer group"
                onClick={() => navigate(module.path)}
              >
                <CardHeader className="pb-3">
                  <div className={`w-12 h-12 rounded-lg ${module.color} flex items-center justify-center mb-3 group-hover:scale-110 transition-transform`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <CardTitle className="text-lg font-semibold text-gray-800">
                    {module.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">{module.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">快速統計</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">123</div>
              <div className="text-sm text-gray-600">總會員數</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">45</div>
              <div className="text-sm text-gray-600">今日預約</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">8</div>
              <div className="text-sm text-gray-600">服務項目</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-600">12</div>
              <div className="text-sm text-gray-600">活動票券</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;