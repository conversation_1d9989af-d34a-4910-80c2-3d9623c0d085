# LINE Login 設定指南

## 問題說明

如果您看到 "Invalid redirect_uri value. Check if it is registered in a LINE developers site" 錯誤，這表示 redirect_uri 沒有在 LINE 開發者控制台正確註冊。

## 解決步驟

### 1. 前往 LINE Developers Console

1. 訪問 [LINE Developers Console](https://developers.line.biz/console/)
2. 使用您的 LINE 帳號登入

### 2. 創建或選擇 Provider

1. 如果是第一次使用，點擊 "Create a new provider"
2. 輸入 Provider 名稱（例如：您的公司名稱）
3. 如果已有 Provider，直接選擇

### 3. 創建 LINE Login Channel

1. 點擊 "Create a new channel"
2. 選擇 "LINE Login"
3. 填寫以下資訊：
   - **Channel name**: 您的應用程式名稱
   - **Channel description**: 應用程式描述
   - **App type**: 選擇 "Web app"
   - **Email address**: 您的聯絡信箱

### 4. 設定 Callback URL

1. 在 Channel 設定頁面，找到 "LINE Login settings"
2. 在 "Callback URL" 欄位中添加：
   ```
   https://line-beauty-appoint.lovable.app/auth/line/callback
   ```

   **注意**:
   - 生產環境使用 `https://line-beauty-appoint.lovable.app`
   - 如需本地開發，可另外添加 `http://localhost:8082`

### 5. 獲取 Channel 資訊

1. 在 "Basic settings" 頁籤中找到：
   - **Channel ID**: 複製此值
   - **Channel secret**: 點擊 "Issue" 生成，然後複製

### 6. 在系統中設定

1. 前往系統後台的 "LINE API 設定" 頁面
2. 填入剛才獲取的 Channel ID 和 Channel Secret
3. 點擊 "自動設定" 按鈕來設定正確的 Redirect URI
4. 點擊 "儲存設定"

## 常見問題

### Q: 為什麼會出現 redirect_uri 錯誤？
A: 這是因為 LINE 的安全機制，所有的 callback URL 都必須事先在開發者控制台註冊。

### Q: 開發和生產環境需要不同的設定嗎？
A: 是的，開發環境通常使用 localhost，生產環境需要使用實際的域名。您可能需要創建兩個不同的 Channel 或在同一個 Channel 中註冊多個 callback URL。

### Q: 可以註冊多個 callback URL 嗎？
A: 是的，您可以在 LINE Developers Console 中註冊多個 callback URL，每行一個。

## 範例設定

### 生產環境 (Lovable)
```
https://line-beauty-appoint.lovable.app/auth/line/callback
```

### 開發環境 (可選)
```
http://localhost:8082/auth/line/callback
```

## 測試登入

設定完成後：
1. 確保所有設定都已儲存
2. 前往登入頁面
3. 點擊 "使用 LINE 登入" 按鈕
4. 應該會正確導向到 LINE 授權頁面

如果仍有問題，請檢查：
- Channel ID 和 Channel Secret 是否正確
- Callback URL 是否完全匹配（包括協議、域名、端口、路徑）
- LINE Channel 是否已啟用
