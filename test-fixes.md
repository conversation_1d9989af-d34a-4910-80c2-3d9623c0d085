# 修正驗證測試

## 修正1：LINE登入會員資料連結測試

### 測試步驟：
1. 使用LINE登入功能
2. 檢查登入成功後是否在 `profiles` 表中創建了對應記錄
3. 進入會員管理頁面，確認能看到LINE登入的用戶
4. 進入個人資料頁面，確認能正確顯示用戶資訊

### 預期結果：
- LINE登入成功後，`profiles` 表中應該有對應的用戶記錄
- 會員管理頁面能正確顯示LINE用戶的資料
- 個人資料頁面能正確載入用戶資訊

## 修正2：預約時段顯示測試

### 測試步驟：
1. 進入預約頁面
2. 選擇日期
3. 檢查時段是否正確顯示
4. 確認時段格式為 "HH:MM"（如 "09:00"）
5. 測試時段可用性判斷是否正確

### 預期結果：
- 時段能正確顯示，格式為 "HH:MM"
- 已預約的時段應該顯示為不可用
- 過去的時段（當天）應該顯示為不可用

## 調試資訊

### 控制台日誌檢查：
- 查看 "載入的時段資料" 日誌
- 查看 "已預約時段" 日誌
- 查看 "User profile created successfully" 或 "User profile updated successfully" 日誌

### 資料庫檢查：
```sql
-- 檢查 profiles 表中的LINE用戶
SELECT * FROM profiles WHERE email LIKE '%line_user_%';

-- 檢查時段資料
SELECT id, start_time, duration_minutes, is_active FROM time_slots 
WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' AND is_active = true;

-- 檢查預約資料
SELECT booking_date, booking_time, status FROM bookings 
WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
```
