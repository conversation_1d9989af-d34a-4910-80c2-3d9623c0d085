# LINE 美容預約系統 - 專案規格文件

## 📋 專案概述

### 專案名稱
LINE Beauty Appointment System (line-beauty-appoint)

### 專案描述
一個基於 LINE Login 的美容院預約管理系統，提供用戶預約服務、會員管理、優惠券系統等功能。

### 部署架構
- **開發平台**: Lovable.dev
- **部署環境**: Lovable.dev 伺服器
- **生產網址**: https://line-beauty-appoint.lovable.app/
- **資料庫**: Supabase (jyehyqcjusiywggofpld.supabase.co)
- **版本控制**: GitHub 同步到本地開發

## 🏗️ 技術架構

### 前端技術棧
```
- React 18.3.1 + TypeScript
- Vite (建置工具)
- Tailwind CSS + shadcn/ui (UI 框架)
- React Router (路由管理)
- TanStack Query (狀態管理)
- React Hook Form + Zod (表單驗證)
```

### 後端技術棧
```
- Supabase (BaaS)
  - PostgreSQL 資料庫
  - Authentication (Auth)
  - Edge Functions (Serverless)
  - Real-time subscriptions
```

### 第三方整合
```
- LINE Login API (OAuth 2.0)
- LINE Messaging API (Webhook)
```

## 📊 系統架構圖

```mermaid
graph TB
    A[用戶瀏覽器] --> B[Lovable.dev 前端]
    B --> C[Supabase Backend]
    B --> D[LINE Login API]
    C --> E[PostgreSQL 資料庫]
    C --> F[Edge Functions]
    F --> G[LINE Messaging API]
    
    H[GitHub Repository] --> I[本地開發環境]
    I --> H
    H --> B
    B --> H
```

## 🗃️ 資料庫設計

### 核心資料表

#### 1. 用戶相關
- `profiles` - 用戶基本資料
- `admin_users` - 管理員用戶

#### 2. 商店相關
- `shops` - 商店資訊
- `line_settings` - LINE API 設定

#### 3. 服務相關
- `categories` - 服務分類
- `services` - 服務項目
- `service_providers` - 服務提供者
- `time_slots` - 時間段設定

#### 4. 預約相關
- `bookings` - 預約記錄
- `booking_items` - 預約項目明細

#### 5. 優惠相關
- `coupons` - 優惠券
- `coupon_usage` - 優惠券使用記錄
- `loyalty_points` - 忠誠度積分

#### 6. 財務相關
- `wallet_transactions` - 錢包交易記錄

### 資料庫關聯圖

```mermaid
erDiagram
    profiles ||--o{ bookings : "用戶預約"
    shops ||--o{ services : "商店服務"
    shops ||--o{ time_slots : "時間段"
    shops ||--o{ line_settings : "LINE設定"
    categories ||--o{ services : "服務分類"
    services ||--o{ booking_items : "預約項目"
    bookings ||--o{ booking_items : "預約明細"
    coupons ||--o{ coupon_usage : "優惠券使用"
    profiles ||--o{ wallet_transactions : "錢包交易"
```

## 🎯 功能模組

### 1. 用戶端功能

#### 認證模組
- LINE Login 整合
- 用戶註冊/登入
- 會話管理

#### 預約模組
- 服務選擇
- 時間選擇
- 預約確認
- 預約管理

#### 會員模組
- 個人資料管理
- 錢包餘額
- 積分系統
- 優惠券管理

### 2. 管理端功能

#### 系統管理
- LINE API 設定
- 用戶管理
- 權限管理

#### 業務管理
- 服務管理
- 時間段管理
- 員工管理
- 預約管理

#### 行銷管理
- 優惠券管理
- 積分設定
- 數據分析

## 🔐 認證流程

### LINE Login 流程圖

```mermaid
sequenceDiagram
    participant U as 用戶
    participant F as 前端應用
    participant L as LINE API
    participant E as Edge Function
    participant S as Supabase Auth
    
    U->>F: 點擊 LINE 登入
    F->>F: 生成 state 參數
    F->>L: 導向 LINE 授權頁面
    L->>U: 顯示授權確認
    U->>L: 確認授權
    L->>F: 回調帶 code 和 state
    F->>F: 驗證 state 參數
    F->>E: 發送 code 交換 token
    E->>L: 交換 access token
    L->>E: 返回 token 和用戶資料
    E->>F: 返回用戶資料
    F->>S: 創建/登入 Supabase 用戶
    S->>F: 返回認證狀態
    F->>U: 登入成功，導向服務頁面
```

## 🚀 部署流程

### 開發流程
```mermaid
graph LR
    A[本地開發] --> B[Git Push]
    B --> C[GitHub Repository]
    C --> D[Lovable.dev 自動部署]
    D --> E[生產環境]
    
    F[Lovable.dev 編輯] --> G[自動提交到 GitHub]
    G --> C
```

### 環境配置
- **開發環境**: 本地 Vite 開發伺服器 (Port 8080)
- **生產環境**: Lovable.dev 託管
- **資料庫**: Supabase 雲端服務

## 📱 用戶介面設計

### 設計系統
- **主色調**: 粉紅色系 (HSL 340, 75%, 55%)
- **字體**: 系統預設字體
- **響應式設計**: 移動優先
- **UI 框架**: shadcn/ui + Tailwind CSS

### 頁面結構
```
/                    - 歡迎頁面
/login              - 登入頁面
/auth/line/callback - LINE 登入回調
/services           - 服務選擇
/booking-time       - 時間選擇
/booking-confirm    - 預約確認
/booking-success    - 預約成功
/profile            - 個人資料
/wallet             - 錢包餘額
/admin              - 管理後台
/admin/*            - 各種管理功能
```

## 🔧 開發工具與配置

### 開發環境
- Node.js + npm
- TypeScript 5.5.3
- ESLint + Prettier
- Vite 5.4.1

### 專案結構
```
src/
├── components/     # 可重用組件
├── pages/         # 頁面組件
├── hooks/         # 自定義 Hooks
├── utils/         # 工具函數
├── contexts/      # React Context
├── integrations/  # 第三方整合
└── lib/          # 共用函式庫
```

## 🔍 品質保證

### 錯誤處理
- 全域錯誤邊界
- API 錯誤處理
- 用戶友好的錯誤訊息

### 診斷工具
- LINE Login 診斷頁面
- Edge Function 連線測試
- Email 驗證診斷
- State 驗證診斷

### 安全性
- CSRF 防護 (state 參數)
- JWT Token 驗證
- Row Level Security (RLS)
- 輸入驗證與清理

## 📈 效能優化

### 前端優化
- 代碼分割 (Code Splitting)
- 懶加載 (Lazy Loading)
- 圖片優化
- 快取策略

### 後端優化
- 資料庫索引
- 查詢優化
- Edge Functions 快取

## 🔮 未來規劃

### 短期目標
- 完善 LINE Login 整合
- 優化用戶體驗
- 增加錯誤處理

### 中期目標
- 添加支付功能
- 實現推播通知
- 數據分析儀表板

### 長期目標
- 多店鋪支援
- 移動應用程式
- AI 智能推薦

---

**文檔版本**: v1.0
**最後更新**: 2025-07-14
**維護者**: <EMAIL>
