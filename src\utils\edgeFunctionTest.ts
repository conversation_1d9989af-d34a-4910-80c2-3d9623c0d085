/**
 * Edge Function 連線測試工具
 * 專門用於診斷 Supabase Edge Function 連線問題
 */

import { supabase } from "@/integrations/supabase/client";

export interface EdgeFunctionTestResult {
  step: string;
  success: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

/**
 * 測試 Edge Function 連線的多種方法
 */
export const testEdgeFunctionConnection = async (): Promise<EdgeFunctionTestResult[]> => {
  const results: EdgeFunctionTestResult[] = [];
  const timestamp = new Date().toISOString();

  // 1. 基本連線測試
  try {
    const response = await fetch('https://jyehyqcjusiywggofpld.supabase.co/functions/v1/line-login-token', {
      method: 'OPTIONS',
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp5ZWh5cWNqdXNpeXdnZ29mcGxkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2ODg4MjAsImV4cCI6MjA2NzI2NDgyMH0.AC-58WjFXUwRJ98zAN3W--t1JCSgmv5cy7KJBCJSBj0',
        'Content-Type': 'application/json'
      }
    });

    results.push({
      step: "基本連線測試",
      success: response.ok,
      message: response.ok ? "Edge Function 端點可以訪問" : `HTTP ${response.status}: ${response.statusText}`,
      details: {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      },
      timestamp
    });
  } catch (error) {
    results.push({
      step: "基本連線測試",
      success: false,
      message: "無法連接到 Edge Function 端點",
      details: error,
      timestamp
    });
  }

  // 2. Supabase 客戶端測試
  try {
    const { data, error } = await supabase.functions.invoke('line-login-token', {
      body: { test: true }
    });

    const isSuccess = !error && data && (data.status === 'ok' || data.message);
    
    results.push({
      step: "Supabase 客戶端測試",
      success: isSuccess,
      message: error ? `調用失敗: ${error.message}` : (
        isSuccess ? "Supabase 客戶端可以調用 Edge Function" : "Edge Function 回應異常"
      ),
      details: error || data,
      timestamp
    });
  } catch (error) {
    results.push({
      step: "Supabase 客戶端測試",
      success: false,
      message: "Supabase 客戶端調用時發生錯誤",
      details: error,
      timestamp
    });
  }

  // 3. 網路環境檢查
  try {
    const response = await fetch('https://httpbin.org/get');
    results.push({
      step: "網路環境檢查",
      success: response.ok,
      message: response.ok ? "網路連線正常" : "網路連線可能有問題",
      details: { canAccessExternalAPI: response.ok },
      timestamp
    });
  } catch (error) {
    results.push({
      step: "網路環境檢查",
      success: false,
      message: "無法訪問外部 API，可能有網路限制",
      details: error,
      timestamp
    });
  }

  // 4. CORS 檢查
  try {
    const response = await fetch('https://jyehyqcjusiywggofpld.supabase.co/functions/v1/line-login-token', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp5ZWh5cWNqdXNpeXdnZ29mcGxkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2ODg4MjAsImV4cCI6MjA2NzI2NDgyMH0.AC-58WjFXUwRJ98zAN3W--t1JCSgmv5cy7KJBCJSBj0',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: "test_code",
        redirectUri: "https://test.com/callback",
        channelId: "test_channel",
        channelSecret: "test_secret"
      })
    });

    const responseText = await response.text();
    
    results.push({
      step: "CORS 和 POST 請求測試",
      success: response.status !== 0, // 0 通常表示 CORS 錯誤
      message: response.status === 0 ? "CORS 錯誤" : `HTTP ${response.status}`,
      details: {
        status: response.status,
        statusText: response.statusText,
        responseText: responseText.substring(0, 200) // 只顯示前200字符
      },
      timestamp
    });
  } catch (error) {
    results.push({
      step: "CORS 和 POST 請求測試",
      success: false,
      message: "POST 請求失敗",
      details: error,
      timestamp
    });
  }

  return results;
};

/**
 * 生成診斷報告
 */
export const generateDiagnosticReport = (results: EdgeFunctionTestResult[]): string => {
  let report = "=== Edge Function 連線診斷報告 ===\n\n";
  
  results.forEach((result, index) => {
    report += `${index + 1}. ${result.step}\n`;
    report += `   狀態: ${result.success ? '✅ 成功' : '❌ 失敗'}\n`;
    report += `   訊息: ${result.message}\n`;
    if (result.details) {
      report += `   詳細: ${JSON.stringify(result.details, null, 2)}\n`;
    }
    report += `   時間: ${result.timestamp}\n\n`;
  });

  // 總結
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  report += "=== 總結 ===\n";
  report += `成功: ${successCount}/${totalCount}\n`;
  
  if (successCount === totalCount) {
    report += "✅ 所有測試都通過，Edge Function 連線正常\n";
  } else {
    report += "❌ 部分測試失敗，請檢查失敗的項目\n";
    
    // 提供建議
    const failedSteps = results.filter(r => !r.success).map(r => r.step);
    report += "\n建議檢查項目:\n";
    
    if (failedSteps.includes("基本連線測試")) {
      report += "- 檢查網路連線\n";
      report += "- 確認 Supabase 專案 URL 正確\n";
    }
    
    if (failedSteps.includes("Supabase 客戶端測試")) {
      report += "- 檢查 Supabase API Key\n";
      report += "- 確認 Edge Function 已正確部署\n";
    }
    
    if (failedSteps.includes("網路環境檢查")) {
      report += "- 檢查防火牆設定\n";
      report += "- 確認沒有網路代理阻擋\n";
    }
    
    if (failedSteps.includes("CORS 和 POST 請求測試")) {
      report += "- 檢查 Edge Function 的 CORS 設定\n";
      report += "- 確認瀏覽器沒有阻擋跨域請求\n";
    }
  }

  return report;
};

/**
 * 在控制台輸出診斷報告
 */
export const logDiagnosticReport = async (): Promise<void> => {
  console.group("🔧 Edge Function 連線診斷");
  
  const results = await testEdgeFunctionConnection();
  const report = generateDiagnosticReport(results);
  
  console.log(report);
  console.groupEnd();
};

/**
 * 快速檢查 Edge Function 是否可用
 */
export const quickEdgeFunctionCheck = async (): Promise<boolean> => {
  try {
    const { error } = await supabase.functions.invoke('line-login-token', {
      body: { test: true }
    });
    return !error;
  } catch {
    return false;
  }
};
