import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Clock, DollarSign } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "@/hooks/use-toast";

interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
}

interface Service {
  id: string;
  name: string;
  description: string | null;
  duration_minutes: number;
  price_min: number;
  price_max: number | null;
  tag: string | null;
  category_id: string;
}

const ServiceSelection = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const [loading, setLoading] = useState(true);

  // 檢查登入狀態
  useEffect(() => {
    if (!user) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行預約",
        variant: "destructive",
      });
      navigate("/login");
      return;
    }
  }, [user, navigate]);

  // 載入服務分類
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from('service_categories')
          .select('id, name, slug')
          .eq('is_active', true)
          .order('sort_order');

        if (error) throw error;

        setCategories(data || []);
        if (data && data.length > 0) {
          setSelectedCategory(data[0].id);
        }
      } catch (error: any) {
        toast({
          title: "載入分類失敗",
          description: error.message,
          variant: "destructive",
        });
      }
    };

    fetchCategories();
  }, []);

  // 載入服務項目
  useEffect(() => {
    const fetchServices = async () => {
      if (!selectedCategory) return;

      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('services')
          .select('*')
          .eq('category_id', selectedCategory)
          .eq('is_active', true)
          .order('sort_order');

        if (error) throw error;

        setServices(data || []);
      } catch (error: any) {
        toast({
          title: "載入服務失敗",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [selectedCategory]);

  const handleServiceToggle = (serviceId: string, price: number) => {
    if (selectedServices.includes(serviceId)) {
      setSelectedServices(selectedServices.filter(id => id !== serviceId));
      setTotalPrice(totalPrice - price);
    } else {
      setSelectedServices([...selectedServices, serviceId]);
      setTotalPrice(totalPrice + price);
    }
  };

  const handleNext = () => {
    if (selectedServices.length > 0) {
      // 將選擇的服務存儲到 localStorage，供後續頁面使用
      localStorage.setItem('selectedServices', JSON.stringify(selectedServices));
      localStorage.setItem('totalPrice', totalPrice.toString());
      navigate("/booking-time");
    }
  };

  const getServiceImage = (serviceName: string) => {
    if (serviceName.includes('美甲')) return '💅';
    if (serviceName.includes('美睫')) return '👁️';
    if (serviceName.includes('頭皮') || serviceName.includes('護理')) return '💆‍♀️';
    return '✨';
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}小時${mins}分鐘`;
    } else if (hours > 0) {
      return `${hours}小時`;
    } else {
      return `${mins}分鐘`;
    }
  };

  const formatPrice = (service: Service) => {
    if (service.price_max && service.price_max !== service.price_min) {
      return `$${service.price_min}-${service.price_max}元`;
    } else {
      return `$${service.price_min}元`;
    }
  };

  if (!user) {
    return null; // 避免在導向過程中閃爍
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-sm border-b border-border z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-3">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate(-1)}
              className="rounded-full"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <p className="text-sm text-muted-foreground">取消預約</p>
            </div>
          </div>
          <h1 className="font-semibold">Hello 歡迎預約</h1>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Service Category Selection */}
        <div>
          <h2 className="text-lg font-medium mb-4">請選擇服務項目</h2>
          <div className="flex space-x-2 overflow-x-auto">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className={`whitespace-nowrap rounded-full ${
                  selectedCategory === category.id 
                    ? "beauty-gradient text-white" 
                    : "bg-background hover:bg-accent"
                }`}
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Services List */}
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">載入中...</p>
            </div>
          ) : (
            <>
              <h3 className="font-medium text-foreground">
                {categories.find(cat => cat.id === selectedCategory)?.name}
              </h3>
              
              {services.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">此分類暫無可用服務</p>
                </div>
              ) : (
                services.map((service) => (
                  <Card 
                    key={service.id}
                    className={`p-4 cursor-pointer smooth-transition hover:shadow-md ${
                      selectedServices.includes(service.id) 
                        ? "ring-2 ring-primary bg-primary-lighter" 
                        : "card-shadow"
                    }`}
                    onClick={() => handleServiceToggle(service.id, service.price_min)}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="text-3xl">{getServiceImage(service.name)}</div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-foreground mb-1">{service.name}</h4>
                            {service.description && (
                              <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                            )}
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4" />
                                <span>{formatDuration(service.duration_minutes)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <DollarSign className="w-4 h-4" />
                                <span>{formatPrice(service)} 起</span>
                              </div>
                            </div>
                            {service.tag && (
                              <Badge 
                                variant="secondary" 
                                className="mt-2 text-xs"
                              >
                                #{service.tag}
                              </Badge>
                            )}
                          </div>
                          {selectedServices.includes(service.id) && (
                            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </>
          )}
        </div>
      </div>

      {/* Bottom Summary & Action */}
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border p-4">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-muted-foreground">
            已選 {selectedServices.length} 項服務：{totalPrice} 元起
          </span>
          <Button variant="ghost" size="sm" onClick={() => {
            setSelectedServices([]);
            setTotalPrice(0);
          }}>
            清空
          </Button>
        </div>
        <Button 
          className="w-full h-12 beauty-gradient text-white rounded-full font-medium smooth-transition disabled:opacity-50"
          onClick={handleNext}
          disabled={selectedServices.length === 0}
        >
          <ArrowLeft className="w-4 h-4 mr-2 rotate-180" />
          選擇日期與時間
          <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
        </Button>
      </div>
    </div>
  );
};

export default ServiceSelection;