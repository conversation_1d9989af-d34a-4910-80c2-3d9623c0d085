import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Edit, Trash2, Upload } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";

const StaffManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [staff, setStaff] = useState([
    {
      id: 1,
      name: "王美麗",
      avatar: "",
      description: "專業美容師，擁有10年經驗，擅長臉部保養與護理",
      specialties: ["臉部保養", "深度清潔", "抗老護理"],
      isActive: true
    },
    {
      id: 2,
      name: "李專業",
      avatar: "",
      description: "按摩治療師，專精於身體放鬆與舒壓按摩",
      specialties: ["身體按摩", "穴位按摩", "芳療護理"],
      isActive: true
    }
  ]);

  const [editingStaff, setEditingStaff] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleSaveStaff = (staffData: any) => {
    if (editingStaff?.id) {
      // 編輯現有員工
      setStaff(staff.map(s => s.id === editingStaff.id ? { ...staffData, id: editingStaff.id } : s));
      toast({
        title: "更新成功",
        description: "員工資料已更新"
      });
    } else {
      // 新增員工
      const newStaff = {
        ...staffData,
        id: Math.max(...staff.map(s => s.id)) + 1
      };
      setStaff([...staff, newStaff]);
      toast({
        title: "新增成功",
        description: "新員工已新增"
      });
    }
    setIsDialogOpen(false);
    setEditingStaff(null);
  };

  const handleDeleteStaff = (id: number) => {
    setStaff(staff.filter(s => s.id !== id));
    toast({
      title: "刪除成功",
      description: "員工已刪除"
    });
  };

  const openEditDialog = (staffMember?: any) => {
    setEditingStaff(staffMember || {
      name: "",
      avatar: "",
      description: "",
      specialties: [],
      isActive: true
    });
    setIsDialogOpen(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-800">服務人員管理</h1>
            <p className="text-gray-600">新增與編輯服務人員資料</p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => openEditDialog()} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                新增員工
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingStaff?.id ? "編輯員工" : "新增員工"}
                </DialogTitle>
              </DialogHeader>
              <StaffForm
                staff={editingStaff}
                onSave={handleSaveStaff}
                onCancel={() => setIsDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {staff.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <Avatar className="w-20 h-20 mx-auto mb-4">
                  <AvatarImage src={member.avatar} />
                  <AvatarFallback className="text-lg">{member.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <CardTitle className="text-xl">{member.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {member.description}
                </p>
                
                <div className="mb-4">
                  <Label className="text-sm font-medium">專長項目：</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {member.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditDialog(member)}
                    className="flex-1"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    編輯
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteStaff(member.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

const StaffForm = ({ staff, onSave, onCancel }: any) => {
  const [formData, setFormData] = useState({
    name: staff?.name || "",
    avatar: staff?.avatar || "",
    description: staff?.description || "",
    specialties: staff?.specialties || [],
    isActive: staff?.isActive ?? true
  });

  const [specialtyInput, setSpecialtyInput] = useState("");

  const addSpecialty = () => {
    if (specialtyInput.trim() && !formData.specialties.includes(specialtyInput.trim())) {
      setFormData({
        ...formData,
        specialties: [...formData.specialties, specialtyInput.trim()]
      });
      setSpecialtyInput("");
    }
  };

  const removeSpecialty = (index: number) => {
    setFormData({
      ...formData,
      specialties: formData.specialties.filter((_, i) => i !== index)
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">姓名</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
      </div>

      <div>
        <Label htmlFor="description">簡介</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          rows={3}
        />
      </div>

      <div>
        <Label>專長項目</Label>
        <div className="flex gap-2 mt-1">
          <Input
            value={specialtyInput}
            onChange={(e) => setSpecialtyInput(e.target.value)}
            placeholder="輸入專長項目"
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecialty())}
          />
          <Button type="button" onClick={addSpecialty} size="sm">
            新增
          </Button>
        </div>
        <div className="flex flex-wrap gap-1 mt-2">
          {formData.specialties.map((specialty, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full cursor-pointer hover:bg-pink-200"
              onClick={() => removeSpecialty(index)}
            >
              {specialty} ×
            </span>
          ))}
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" className="flex-1">
          儲存
        </Button>
        <Button type="button" variant="outline" onClick={onCancel}>
          取消
        </Button>
      </div>
    </form>
  );
};

export default StaffManagement;