-- 修正 make_current_user_admin 函數的 ON CONFLICT 問題
-- 並添加必要的約束條件

-- 首先添加複合唯一約束（如果不存在）
DO $$
BEGIN
  -- 檢查是否已存在複合唯一約束
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'admin_users_user_shop_unique'
  ) THEN
    -- 添加複合唯一約束
    ALTER TABLE public.admin_users 
    ADD CONSTRAINT admin_users_user_shop_unique 
    UNIQUE (user_id, shop_id);
  END IF;
END $$;

-- 重新創建修正後的函數
CREATE OR REPLACE FUNCTION public.make_current_user_admin()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
  shop_uuid uuid;
  existing_admin_id uuid;
BEGIN
  -- 獲取當前用戶 ID
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RAISE NOTICE 'No authenticated user found';
    RETURN false;
  END IF;
  
  -- 獲取測試店家 ID
  SELECT id INTO shop_uuid FROM public.shops WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
  
  IF shop_uuid IS NULL THEN
    RAISE NOTICE 'Shop not found';
    RETURN false;
  END IF;
  
  -- 檢查用戶是否已經是管理員
  SELECT id INTO existing_admin_id 
  FROM public.admin_users 
  WHERE user_id = current_user_id AND shop_id = shop_uuid;
  
  IF existing_admin_id IS NOT NULL THEN
    -- 用戶已經是管理員，確保狀態為啟用
    UPDATE public.admin_users 
    SET is_active = true, updated_at = now()
    WHERE id = existing_admin_id;
    
    RAISE NOTICE 'User is already an admin, status updated';
    RETURN true;
  END IF;
  
  -- 將用戶添加為管理員
  BEGIN
    INSERT INTO public.admin_users (user_id, shop_id, role, is_active)
    VALUES (current_user_id, shop_uuid, 'admin', true);
    
    RAISE NOTICE 'User successfully added as admin';
    RETURN true;
  EXCEPTION
    WHEN unique_violation THEN
      -- 如果發生唯一約束違反，嘗試更新現有記錄
      UPDATE public.admin_users 
      SET is_active = true, updated_at = now()
      WHERE user_id = current_user_id AND shop_id = shop_uuid;
      
      RAISE NOTICE 'Admin record updated due to unique constraint';
      RETURN true;
    WHEN OTHERS THEN
      RAISE NOTICE 'Error adding admin: %', SQLERRM;
      RETURN false;
  END;
END;
$$;

-- 創建一個簡化版本的函數，直接插入而不使用 ON CONFLICT
CREATE OR REPLACE FUNCTION public.make_current_user_admin_simple()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
  shop_uuid uuid;
BEGIN
  -- 獲取當前用戶 ID
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RETURN false;
  END IF;
  
  -- 獲取測試店家 ID
  shop_uuid := 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
  
  -- 檢查用戶是否已經是管理員
  IF EXISTS (
    SELECT 1 FROM public.admin_users 
    WHERE user_id = current_user_id AND shop_id = shop_uuid
  ) THEN
    -- 確保狀態為啟用
    UPDATE public.admin_users 
    SET is_active = true, updated_at = now()
    WHERE user_id = current_user_id AND shop_id = shop_uuid;
    RETURN true;
  END IF;
  
  -- 添加新的管理員記錄
  INSERT INTO public.admin_users (user_id, shop_id, role, is_active)
  VALUES (current_user_id, shop_uuid, 'admin', true);
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- 創建一個檢查當前用戶是否為管理員的函數
CREATE OR REPLACE FUNCTION public.check_current_user_admin()
RETURNS TABLE(
  is_admin boolean,
  user_id uuid,
  shop_id uuid,
  role text,
  is_active boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
BEGIN
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RETURN QUERY SELECT false, null::uuid, null::uuid, null::text, false;
    RETURN;
  END IF;
  
  RETURN QUERY
  SELECT 
    true as is_admin,
    au.user_id,
    au.shop_id,
    au.role,
    au.is_active
  FROM public.admin_users au
  WHERE au.user_id = current_user_id 
    AND au.shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
    AND au.is_active = true;
    
  -- 如果沒有找到記錄，返回 false
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, current_user_id, 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid, null::text, false;
  END IF;
END;
$$;
