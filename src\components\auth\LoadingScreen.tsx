import { Loader2 } from "lucide-react";

interface LoadingScreenProps {
  title: string;
  description: string;
}

export const LoadingScreen = ({ title, description }: LoadingScreenProps) => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-pink-soft to-background p-4">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
};