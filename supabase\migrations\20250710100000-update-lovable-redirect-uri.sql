-- Update LINE redirect URI to Lovable production environment
-- This migration updates the redirect_uri to match the actual deployment URL

-- Update the redirect URI for the test shop to match Lovable deployment
UPDATE public.line_settings 
SET redirect_uri = 'https://line-beauty-appoint.lovable.app/auth/line/callback'
WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

-- If no record exists, insert a default one with the correct production URL
INSERT INTO public.line_settings (shop_id, channel_id, channel_secret, redirect_uri)
VALUES (
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '',
  '',
  'https://line-beauty-appoint.lovable.app/auth/line/callback'
) ON CONFLICT (shop_id) DO UPDATE SET
  redirect_uri = EXCLUDED.redirect_uri;

-- Add a helpful comment
COMMENT ON COLUMN public.line_settings.redirect_uri IS 'The callback URL for LINE Login OAuth flow. Current production URL: https://line-beauty-appoint.lovable.app/auth/line/callback';
