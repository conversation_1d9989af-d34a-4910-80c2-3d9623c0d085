# LINE Login 問題排除指南

## 問題：Invalid redirect_uri value

當您看到 "Invalid redirect_uri value. Check if it is registered in a LINE developers site" 錯誤時，請按照以下步驟解決：

## 🔧 快速修復步驟

### 1. 檢查當前設定
1. 前往 `/admin/line-test` 頁面查看診斷結果
2. 或前往 `/admin/line-settings` 頁面檢查設定

### 2. 修正 Redirect URI
1. 在 LINE Settings 頁面點擊 "自動設定" 按鈕
2. 確保 Redirect URI 設定為：`https://line-beauty-appoint.lovable.app/auth/line/callback`

### 3. 在 LINE Developers Console 註冊 Callback URL

#### 步驟 A：登入 LINE Developers Console
1. 前往 [LINE Developers Console](https://developers.line.biz/console/)
2. 使用您的 LINE 帳號登入

#### 步驟 B：找到您的 LINE Login Channel
1. 選擇您的 Provider
2. 找到 LINE Login 類型的 Channel

#### 步驟 C：設定 Callback URL
1. 在 Channel 設定中找到 "LINE Login settings"
2. 在 "Callback URL" 欄位添加：
   ```
   https://line-beauty-appoint.lovable.app/auth/line/callback
   ```
3. 點擊 "Update" 儲存設定

### 4. 測試設定
1. 在 LINE Settings 頁面點擊 "測試 LINE Login" 按鈕
2. 或直接嘗試登入流程

## 🛠️ 進階除錯工具

### 診斷頁面功能
- **環境檢查**：自動檢測當前運行環境
- **設定驗證**：驗證所有必要的 LINE 設定
- **問題識別**：自動識別常見問題
- **修復建議**：提供具體的修復步驟

### 瀏覽器控制台除錯
在瀏覽器控制台中執行：
```javascript
// 查看除錯資訊
window.debugLineLogin?.();
```

## 📋 檢查清單

在 LINE Developers Console 中確認：
- [ ] Channel ID 已正確複製到系統設定
- [ ] Channel Secret 已正確複製到系統設定
- [ ] Callback URL 已註冊：`https://line-beauty-appoint.lovable.app/auth/line/callback`
- [ ] LINE Login Channel 狀態為 "Published"

在系統設定中確認：
- [ ] Channel ID 已填入
- [ ] Channel Secret 已填入
- [ ] Redirect URI 設定為：`https://line-beauty-appoint.lovable.app/auth/line/callback`

## 🚀 生產環境部署

當部署到生產環境時：
1. 更新 Redirect URI 為您的實際域名
2. 在 LINE Developers Console 中註冊生產環境的 Callback URL
3. 確保使用 HTTPS

範例生產環境設定：
```
https://yourdomain.com/auth/line/callback
```

## ❓ 常見問題

**Q: 為什麼本地開發需要註冊 localhost？**
A: LINE 的安全機制要求所有 callback URL 都必須事先註冊，包括開發環境的 localhost。

**Q: 可以同時註冊多個 callback URL 嗎？**
A: 是的，您可以在 LINE Developers Console 中註冊多個 URL，每行一個。

**Q: 端口號重要嗎？**
A: 是的，URL 必須完全匹配。生產環境使用 HTTPS 443 端口（預設）。

**Q: 測試時仍然出錯怎麼辦？**
A: 請檢查瀏覽器控制台的錯誤訊息，並使用診斷頁面進行詳細檢查。

## 📞 需要協助？

如果按照上述步驟仍無法解決問題：
1. 檢查瀏覽器控制台的詳細錯誤訊息
2. 使用診斷頁面查看具體問題
3. 確認 LINE Developers Console 中的所有設定
