import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, CheckCircle, XCircle, AlertTriangle, RefreshCw } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import { 
  runStateValidationDiagnostic, 
  autoFixStateIssues, 
  generateNewState,
  StateValidationResult 
} from "@/utils/stateValidationDiagnostic";
import { 
  generateValidVirtualEmail, 
  validateEmailFormat 
} from "@/utils/lineUserHelper";

const LineLoginDiagnostic = () => {
  const navigate = useNavigate();
  const [diagnostic, setDiagnostic] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runDiagnostic = () => {
    setLoading(true);
    try {
      const result = runStateValidationDiagnostic();
      
      // 增加 Email 驗證測試
      const testLineUserId = "udc45e0923dbafa371c0d88af25ece0b4";
      const generatedEmail = generateValidVirtualEmail(testLineUserId);
      const emailValidation = validateEmailFormat(generatedEmail);
      
      result.results.push({
        step: "虛擬 Email 生成",
        success: emailValidation.valid,
        message: emailValidation.valid 
          ? `生成的虛擬 Email 有效: ${generatedEmail}` 
          : `生成的虛擬 Email 無效: ${emailValidation.errors.join(', ')}`,
        details: { 
          generatedEmail, 
          validation: emailValidation 
        },
        recommendation: !emailValidation.valid ? "檢查 email 生成邏輯" : undefined
      });

      setDiagnostic(result);
      
      toast({
        title: "診斷完成",
        description: `發現 ${result.results.filter(r => !r.success).length} 個問題`,
      });
    } catch (error) {
      console.error("診斷失敗:", error);
      toast({
        title: "診斷失敗",
        description: "無法執行診斷，請檢查控制台錯誤",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const autoFix = () => {
    try {
      const success = autoFixStateIssues();
      if (success) {
        toast({
          title: "自動修復成功",
          description: "已清除問題項目，請重新執行診斷",
        });
        // 重新執行診斷
        setTimeout(() => runDiagnostic(), 1000);
      } else {
        toast({
          title: "自動修復失敗",
          description: "請手動清除瀏覽器儲存空間",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("自動修復失敗:", error);
      toast({
        title: "自動修復失敗",
        description: "發生未預期的錯誤",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  const getStatusBadge = (success: boolean) => {
    return (
      <Badge variant={success ? "default" : "destructive"}>
        {success ? "正常" : "錯誤"}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-soft to-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => navigate(-1)}
            className="rounded-full"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-2xl font-bold text-foreground">LINE 登入診斷</h1>
          <div className="w-10" />
        </div>

        {/* Control Panel */}
        <Card className="p-6">
          <div className="flex gap-4">
            <Button
              onClick={runDiagnostic}
              disabled={loading}
              className="flex-1"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? "診斷中..." : "執行診斷"}
            </Button>
            <Button
              onClick={autoFix}
              variant="outline"
              disabled={!diagnostic || loading}
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              自動修復
            </Button>
          </div>
        </Card>

        {/* Diagnostic Results */}
        {diagnostic && (
          <div className="space-y-4">
            {/* Summary */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">診斷摘要</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-500">
                    {diagnostic.results.filter((r: StateValidationResult) => r.success).length}
                  </div>
                  <div className="text-sm text-muted-foreground">正常項目</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-500">
                    {diagnostic.results.filter((r: StateValidationResult) => !r.success).length}
                  </div>
                  <div className="text-sm text-muted-foreground">問題項目</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${diagnostic.canProceed ? 'text-green-500' : 'text-red-500'}`}>
                    {diagnostic.canProceed ? "可執行" : "有問題"}
                  </div>
                  <div className="text-sm text-muted-foreground">整體狀態</div>
                </div>
              </div>
            </Card>

            {/* Detailed Results */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">詳細結果</h3>
              <div className="space-y-4">
                {diagnostic.results.map((result: StateValidationResult, index: number) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-muted/50 rounded-lg">
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(result.success)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{result.step}</h4>
                        {getStatusBadge(result.success)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {result.message}
                      </p>
                      {result.details && (
                        <div className="text-xs bg-background p-2 rounded border">
                          <pre className="whitespace-pre-wrap">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </div>
                      )}
                      {result.recommendation && (
                        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                          <strong>建議:</strong> {result.recommendation}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Recommendations */}
            {diagnostic.recommendations.length > 0 && (
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">修復建議</h3>
                <div className="space-y-2">
                  {diagnostic.recommendations.map((rec: string, index: number) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="flex-shrink-0 mt-1">
                        <AlertTriangle className="w-4 h-4 text-yellow-500" />
                      </div>
                      <span className="text-sm">{rec}</span>
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>
        )}

        {!diagnostic && (
          <Card className="p-8 text-center">
            <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">尚未執行診斷</h3>
            <p className="text-muted-foreground mb-4">
              點擊「執行診斷」來檢測 LINE 登入相關問題
            </p>
          </Card>
        )}
      </div>
    </div>
  );
};

export default LineLoginDiagnostic;