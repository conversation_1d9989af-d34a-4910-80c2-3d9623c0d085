// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jyehyqcjusiywggofpld.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp5ZWh5cWNqdXNpeXdnZ29mcGxkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2ODg4MjAsImV4cCI6MjA2NzI2NDgyMH0.AC-58WjFXUwRJ98zAN3W--t1JCSgmv5cy7KJBCJSBj0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});