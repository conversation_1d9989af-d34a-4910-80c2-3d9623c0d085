import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, User, Shield, AlertTriangle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";

const DatabaseDebug = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [timeSlots, setTimeSlots] = useState<any[]>([]);
  const [profiles, setProfiles] = useState<any[]>([]);
  const [shops, setShops] = useState<any[]>([]);
  const [adminUsers, setAdminUsers] = useState<any[]>([]);
  const [userPermissions, setUserPermissions] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const checkDatabase = async () => {
    setLoading(true);
    try {
      // 檢查商店資料
      const { data: shopsData, error: shopsError } = await supabase
        .from('shops')
        .select('*');

      console.log('商店資料:', { shopsData, shopsError });
      setShops(shopsData || []);

      // 檢查時段資料
      const { data: slotsData, error: slotsError } = await supabase
        .from('time_slots')
        .select('*')
        .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa');

      console.log('時段資料:', { slotsData, slotsError });
      setTimeSlots(slotsData || []);

      // 檢查用戶資料
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .limit(10);

      console.log('用戶資料:', { profilesData, profilesError });
      setProfiles(profilesData || []);

      // 檢查管理員用戶
      const { data: adminData, error: adminError } = await supabase
        .from('admin_users')
        .select('*');

      console.log('管理員資料:', { adminData, adminError });
      setAdminUsers(adminData || []);

      // 檢查當前用戶權限
      await checkUserPermissions();

      toast({
        title: "檢查完成",
        description: "資料庫狀態已載入",
      });
    } catch (error: any) {
      console.error('檢查資料庫錯誤:', error);
      toast({
        title: "檢查失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const checkUserPermissions = async () => {
    if (!user) return;

    try {
      // 檢查用戶是否為管理員
      const { data: adminCheck, error: adminError } = await supabase
        .from('admin_users')
        .select('*')
        .eq('user_id', user.id)
        .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
        .single();

      console.log('管理員檢查:', { adminCheck, adminError });

      setUserPermissions({
        userId: user.id,
        email: user.email,
        isAdmin: !!adminCheck,
        adminData: adminCheck,
        error: adminError
      });
    } catch (error) {
      console.error('檢查用戶權限錯誤:', error);
    }
  };

  const makeCurrentUserAdmin = async () => {
    if (!user) {
      toast({
        title: "錯誤",
        description: "請先登入",
        variant: "destructive",
      });
      return;
    }

    try {
      // 調用資料庫函數設定管理員權限
      const { data, error } = await supabase.rpc('make_current_user_admin');

      if (error) throw error;

      if (data) {
        toast({
          title: "設定成功",
          description: "您已被設定為管理員",
        });

        // 重新檢查權限
        await checkUserPermissions();
      } else {
        toast({
          title: "設定失敗",
          description: "無法設定管理員權限",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('設定管理員權限錯誤:', error);
      toast({
        title: "設定失敗",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const createTestTimeSlots = async () => {
    try {
      const defaultSlots = [
        { start_time: '09:00', duration_minutes: 60 },
        { start_time: '10:00', duration_minutes: 60 },
        { start_time: '11:00', duration_minutes: 60 },
        { start_time: '14:00', duration_minutes: 60 },
        { start_time: '15:00', duration_minutes: 60 },
        { start_time: '16:00', duration_minutes: 60 },
        { start_time: '17:00', duration_minutes: 60 },
        { start_time: '18:00', duration_minutes: 60 },
      ];

      const { data, error } = await supabase
        .from('time_slots')
        .insert(
          defaultSlots.map(slot => ({
            shop_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
            start_time: slot.start_time,
            duration_minutes: slot.duration_minutes,
            is_active: true
          }))
        )
        .select();

      if (error) throw error;

      toast({
        title: "創建成功",
        description: "測試時段已創建",
      });

      // 重新檢查資料庫
      checkDatabase();
    } catch (error: any) {
      console.error('創建測試時段錯誤:', error);
      toast({
        title: "創建失敗",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    checkDatabase();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/admin")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">資料庫調試</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* 當前用戶權限狀態 */}
        {userPermissions && (
          <Card className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <User className="w-5 h-5 text-primary" />
              <h3 className="font-semibold">當前用戶權限</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>用戶 ID:</span>
                <code className="text-sm bg-muted px-2 py-1 rounded">{userPermissions.userId}</code>
              </div>
              <div className="flex items-center justify-between">
                <span>Email:</span>
                <span>{userPermissions.email}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>管理員權限:</span>
                <Badge variant={userPermissions.isAdmin ? "default" : "destructive"}>
                  {userPermissions.isAdmin ? "是" : "否"}
                </Badge>
              </div>
              {!userPermissions.isAdmin && (
                <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    您沒有管理員權限，這可能導致管理功能無法使用
                  </span>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* 控制按鈕 */}
        <div className="flex gap-4 flex-wrap">
          <Button onClick={checkDatabase} disabled={loading}>
            {loading ? "檢查中..." : "檢查資料庫"}
          </Button>
          <Button onClick={createTestTimeSlots} variant="outline">
            創建測試時段
          </Button>
          {userPermissions && !userPermissions.isAdmin && (
            <Button onClick={makeCurrentUserAdmin} variant="default" className="bg-green-600 hover:bg-green-700">
              <Shield className="w-4 h-4 mr-2" />
              設定為管理員
            </Button>
          )}
        </div>

        {/* 商店資料 */}
        <Card className="p-4">
          <h3 className="font-semibold mb-4">商店資料 ({shops.length})</h3>
          <div className="space-y-2">
            {shops.map((shop) => (
              <div key={shop.id} className="p-2 bg-muted rounded">
                <p><strong>ID:</strong> {shop.id}</p>
                <p><strong>名稱:</strong> {shop.name}</p>
                <p><strong>狀態:</strong> {shop.is_active ? '啟用' : '停用'}</p>
              </div>
            ))}
          </div>
        </Card>

        {/* 時段資料 */}
        <Card className="p-4">
          <h3 className="font-semibold mb-4">時段資料 ({timeSlots.length})</h3>
          <div className="space-y-2">
            {timeSlots.map((slot) => (
              <div key={slot.id} className="p-2 bg-muted rounded">
                <p><strong>時間:</strong> {slot.start_time}</p>
                <p><strong>時長:</strong> {slot.duration_minutes} 分鐘</p>
                <p><strong>狀態:</strong> {slot.is_active ? '啟用' : '停用'}</p>
              </div>
            ))}
          </div>
        </Card>

        {/* 管理員用戶 */}
        <Card className="p-4">
          <h3 className="font-semibold mb-4">管理員用戶 ({adminUsers.length})</h3>
          <div className="space-y-2">
            {adminUsers.map((admin) => (
              <div key={admin.id} className="p-2 bg-muted rounded">
                <p><strong>用戶 ID:</strong> {admin.user_id}</p>
                <p><strong>角色:</strong> {admin.role}</p>
                <p><strong>狀態:</strong> {admin.is_active ? '啟用' : '停用'}</p>
                <p><strong>創建時間:</strong> {new Date(admin.created_at).toLocaleString()}</p>
              </div>
            ))}
          </div>
        </Card>

        {/* 用戶資料 */}
        <Card className="p-4">
          <h3 className="font-semibold mb-4">用戶資料 ({profiles.length})</h3>
          <div className="space-y-2">
            {profiles.map((profile) => (
              <div key={profile.id} className="p-2 bg-muted rounded">
                <p><strong>姓名:</strong> {profile.display_name || '未設定'}</p>
                <p><strong>Email:</strong> {profile.email || '未設定'}</p>
                <p><strong>電話:</strong> {profile.phone || '未設定'}</p>
                <p><strong>用戶 ID:</strong> {profile.user_id}</p>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default DatabaseDebug;
