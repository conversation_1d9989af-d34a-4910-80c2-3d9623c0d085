import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Trash2, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

const TimeSlotManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [shopId, setShopId] = useState<string>("");

  const [businessHours, setBusinessHours] = useState({
    monday: { isOpen: true, openTime: "09:00", closeTime: "18:00" },
    tuesday: { isOpen: true, openTime: "09:00", closeTime: "18:00" },
    wednesday: { isOpen: true, openTime: "09:00", closeTime: "18:00" },
    thursday: { isOpen: true, openTime: "09:00", closeTime: "18:00" },
    friday: { isOpen: true, openTime: "09:00", closeTime: "18:00" },
    saturday: { isOpen: true, openTime: "10:00", closeTime: "17:00" },
    sunday: { isOpen: false, openTime: "10:00", closeTime: "17:00" }
  });

  const [timeSlots, setTimeSlots] = useState<any[]>([]);
  const [newTimeSlot, setNewTimeSlot] = useState({ startTime: "", duration: 60 });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // 使用測試店家 ID
      const currentShopId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
      setShopId(currentShopId);

      // 載入營業時間
      const { data: hours, error: hoursError } = await supabase
        .from('business_hours')
        .select('*')
        .eq('shop_id', currentShopId);

      if (hoursError) {
        console.error('Error loading business hours:', hoursError);
      } else if (hours && hours.length > 0) {
        const newBusinessHours = { ...businessHours };
        const dayMap = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        
        hours.forEach(hour => {
          const dayKey = dayMap[hour.day_of_week] as keyof typeof businessHours;
          newBusinessHours[dayKey] = {
            isOpen: hour.is_open,
            openTime: hour.open_time,
            closeTime: hour.close_time
          };
        });
        setBusinessHours(newBusinessHours);
      }

      // 載入時段
      const { data: slots, error: slotsError } = await supabase
        .from('time_slots')
        .select('*')
        .eq('shop_id', currentShopId)
        .eq('is_active', true)
        .order('start_time');

      if (slotsError) {
        console.error('Error loading time slots:', slotsError);
      } else if (slots) {
        setTimeSlots(slots.map(slot => ({
          id: slot.id,
          startTime: slot.start_time,
          duration: slot.duration_minutes
        })));
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "載入失敗",
        description: `無法載入時間設定: ${error instanceof Error ? error.message : '未知錯誤'}`,
        variant: "destructive"
      });
    }
  };

  const weekDays = [
    { key: "monday", label: "星期一" },
    { key: "tuesday", label: "星期二" },
    { key: "wednesday", label: "星期三" },
    { key: "thursday", label: "星期四" },
    { key: "friday", label: "星期五" },
    { key: "saturday", label: "星期六" },
    { key: "sunday", label: "星期日" }
  ];

  const handleBusinessHourChange = (day: string, field: string, value: any) => {
    setBusinessHours(prev => ({
      ...prev,
      [day]: { ...prev[day as keyof typeof prev], [field]: value }
    }));
  };

  const addTimeSlot = async () => {
    if (!newTimeSlot.startTime || !shopId) {
      toast({
        title: "錯誤",
        description: "請選擇開始時間",
        variant: "destructive"
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('time_slots')
        .insert({
          shop_id: shopId,
          start_time: newTimeSlot.startTime,
          duration_minutes: newTimeSlot.duration
        })
        .select()
        .single();

      if (error) throw error;

      const newSlot = {
        id: data.id,
        startTime: data.start_time,
        duration: data.duration_minutes
      };

      setTimeSlots([...timeSlots, newSlot]);
      setNewTimeSlot({ startTime: "", duration: 60 });
      
      toast({
        title: "新增成功",
        description: "時段已新增"
      });
    } catch (error) {
      console.error('Error adding time slot:', error);
      toast({
        title: "新增失敗",
        description: "無法新增時段",
        variant: "destructive"
      });
    }
  };

  const removeTimeSlot = async (id: string) => {
    try {
      const { error } = await supabase
        .from('time_slots')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setTimeSlots(timeSlots.filter(slot => slot.id !== id));
      toast({
        title: "刪除成功",
        description: "時段已刪除"
      });
    } catch (error) {
      console.error('Error removing time slot:', error);
      toast({
        title: "刪除失敗",
        description: "無法刪除時段",
        variant: "destructive"
      });
    }
  };

  const handleSave = async () => {
    if (!shopId) return;

    setLoading(true);
    try {
      // 更新營業時間
      const dayMap = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const updates = Object.entries(businessHours).map(([day, hours], index) => {
        const dayOfWeek = dayMap.indexOf(day);
        return {
          shop_id: shopId,
          day_of_week: dayOfWeek,
          is_open: hours.isOpen,
          open_time: hours.openTime,
          close_time: hours.closeTime
        };
      });

      const { error } = await supabase
        .from('business_hours')
        .upsert(updates);

      if (error) throw error;

      toast({
        title: "設定已儲存",
        description: "營業時間與時段設定已更新"
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "儲存失敗",
        description: "無法儲存設定",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">預約時間管理</h1>
            <p className="text-gray-600">設定營業時間與可預約時段</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 營業時間設定 */}
          <Card>
            <CardHeader>
              <CardTitle>營業時間設定</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {weekDays.map((day) => (
                <div key={day.key} className="flex items-center justify-between space-x-4 p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Switch
                      checked={businessHours[day.key as keyof typeof businessHours].isOpen}
                      onCheckedChange={(checked) => handleBusinessHourChange(day.key, 'isOpen', checked)}
                    />
                    <Label className="w-16">{day.label}</Label>
                  </div>
                  
                  {businessHours[day.key as keyof typeof businessHours].isOpen && (
                    <div className="flex items-center space-x-2">
                      <Input
                        type="time"
                        value={businessHours[day.key as keyof typeof businessHours].openTime}
                        onChange={(e) => handleBusinessHourChange(day.key, 'openTime', e.target.value)}
                        className="w-24"
                      />
                      <span className="text-gray-500">至</span>
                      <Input
                        type="time"
                        value={businessHours[day.key as keyof typeof businessHours].closeTime}
                        onChange={(e) => handleBusinessHourChange(day.key, 'closeTime', e.target.value)}
                        className="w-24"
                      />
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* 預約時段設定 */}
          <Card>
            <CardHeader>
              <CardTitle>預約時段設定</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-2 p-3 border rounded-lg bg-gray-50">
                  <Input
                    type="time"
                    value={newTimeSlot.startTime}
                    onChange={(e) => setNewTimeSlot({...newTimeSlot, startTime: e.target.value})}
                    className="w-32"
                    placeholder="開始時間"
                  />
                  <Input
                    type="number"
                    value={newTimeSlot.duration}
                    onChange={(e) => setNewTimeSlot({...newTimeSlot, duration: parseInt(e.target.value)})}
                    className="w-24"
                    placeholder="時長"
                    min="15"
                    step="15"
                  />
                  <span className="text-sm text-gray-500">分鐘</span>
                  <Button onClick={addTimeSlot} size="sm" className="ml-auto">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2 max-h-80 overflow-y-auto">
                {timeSlots.map((slot) => (
                  <div key={slot.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <span className="font-medium">{slot.startTime}</span>
                      <span className="text-gray-500 ml-2">({slot.duration} 分鐘)</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTimeSlot(slot.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 flex justify-end">
          <Button 
            onClick={handleSave} 
            className="flex items-center gap-2"
            disabled={loading}
          >
            <Save className="h-4 w-4" />
            {loading ? "儲存中..." : "儲存設定"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TimeSlotManagement;