import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, FileText, CheckCircle, Clock } from "lucide-react";
import { useNavigate } from "react-router-dom";

const BusinessSurvey = () => {
  const navigate = useNavigate();

  const surveys = [
    {
      id: 1,
      title: "服務滿意度調查",
      description: "請分享您對我們服務的寶貴意見",
      status: "pending",
      date: "2024/07/15",
      reward: 50
    },
    {
      id: 2,
      title: "個人資料同意書",
      description: "個人資料蒐集、處理及利用告知事項",
      status: "completed",
      date: "2024/07/10",
      reward: 0
    }
  ];

  const getStatusInfo = (status: string) => {
    if (status === 'completed') {
      return { color: 'text-green-600', bg: 'bg-green-50', text: '已完成', icon: CheckCircle };
    }
    return { color: 'text-orange-600', bg: 'bg-orange-50', text: '待填寫', icon: Clock };
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">商務與同意書</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">2</div>
            <div className="text-sm text-muted-foreground">總數量</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">1</div>
            <div className="text-sm text-muted-foreground">待處理</div>
          </Card>
        </div>

        {/* Survey List */}
        <div className="space-y-3">
          {surveys.map((survey) => {
            const statusInfo = getStatusInfo(survey.status);
            const StatusIcon = statusInfo.icon;
            
            return (
              <Card key={survey.id} className="p-4 card-shadow">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className={`w-10 h-10 ${statusInfo.bg} rounded-lg flex items-center justify-center`}>
                        <FileText className={`w-5 h-5 ${statusInfo.color}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-foreground">{survey.title}</h3>
                        <p className="text-sm text-muted-foreground mt-1">{survey.description}</p>
                        <p className="text-xs text-muted-foreground mt-2">{survey.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                      <Badge variant={survey.status === 'completed' ? 'secondary' : 'destructive'} className="text-xs">
                        {statusInfo.text}
                      </Badge>
                    </div>
                  </div>
                  
                  {survey.reward > 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">完成獎勵</span>
                      <span className="text-sm font-medium text-primary">+${survey.reward} 紅利</span>
                    </div>
                  )}
                  
                  {survey.status === 'pending' && (
                    <Button className="w-full h-10" size="sm">
                      立即填寫
                    </Button>
                  )}
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default BusinessSurvey;