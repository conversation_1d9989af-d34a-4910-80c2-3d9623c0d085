import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Gift, Star, Award, Coffee, Scissors } from "lucide-react";
import { useNavigate } from "react-router-dom";

const RewardsExchange = () => {
  const navigate = useNavigate();

  const userPoints = 1250;
  
  const rewards = [
    {
      id: 1,
      title: "美甲服務 9 折優惠",
      points: 500,
      icon: Scissors,
      color: "text-pink-600",
      bgColor: "bg-pink-50",
      available: true
    },
    {
      id: 2,
      title: "免費飲品一杯",
      points: 300,
      icon: Coffee,
      color: "text-amber-600",
      bgColor: "bg-amber-50",
      available: true
    },
    {
      id: 3,
      title: "VIP 會員升級",
      points: 2000,
      icon: Award,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      available: false
    },
    {
      id: 4,
      title: "生日專屬禮品",
      points: 800,
      icon: Gift,
      color: "text-red-600",
      bgColor: "bg-red-50",
      available: true
    }
  ];

  const pointsHistory = [
    { id: 1, type: "earn", amount: 100, date: "2024/07/15", description: "完成服務評價" },
    { id: 2, type: "earn", amount: 50, date: "2024/07/12", description: "填寫滿意度調查" },
    { id: 3, type: "redeem", amount: -200, date: "2024/07/10", description: "兌換飲品優惠券" },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">紅利兌換</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Points Balance */}
        <Card className="p-6 text-center beauty-gradient text-white">
          <div className="space-y-2">
            <div className="flex items-center justify-center space-x-2">
              <Star className="w-5 h-5" />
              <p className="text-sm opacity-90">我的紅利點數</p>
            </div>
            <p className="text-4xl font-bold">{userPoints}</p>
            <p className="text-xs opacity-75">點數永久有效</p>
          </div>
        </Card>

        {/* Rewards Grid */}
        <div className="space-y-3">
          <h3 className="font-semibold text-foreground">兌換商品</h3>
          
          {rewards.map((reward) => {
            const RewardIcon = reward.icon;
            const canRedeem = userPoints >= reward.points && reward.available;
            
            return (
              <Card key={reward.id} className={`p-4 ${!canRedeem ? 'opacity-60' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-12 h-12 ${reward.bgColor} rounded-lg flex items-center justify-center`}>
                      <RewardIcon className={`w-6 h-6 ${reward.color}`} />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">{reward.title}</h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {reward.points} 點數
                        </Badge>
                        {!reward.available && (
                          <Badge variant="secondary" className="text-xs">
                            暫時缺貨
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button 
                    size="sm" 
                    disabled={!canRedeem}
                    className="h-8 px-4"
                  >
                    {canRedeem ? '兌換' : '不足'}
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Points History */}
        <div className="space-y-3">
          <h3 className="font-semibold text-foreground">點數紀錄</h3>
          
          {pointsHistory.map((history) => (
            <Card key={history.id} className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-foreground">{history.description}</p>
                  <p className="text-sm text-muted-foreground">{history.date}</p>
                </div>
                <div className={`font-semibold ${
                  history.type === 'earn' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {history.type === 'earn' ? '+' : ''}{history.amount}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RewardsExchange;