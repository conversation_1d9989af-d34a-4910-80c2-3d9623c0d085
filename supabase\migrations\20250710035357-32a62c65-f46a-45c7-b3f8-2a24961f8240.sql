-- 創建 LINE API 設定表
CREATE TABLE public.line_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  shop_id uuid NOT NULL REFERENCES public.shops(id) ON DELETE CASCADE,
  channel_id text,
  channel_secret text,
  redirect_uri text,
  new_member_webhook text,
  booking_success_webhook text,
  reminder_webhook text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(shop_id)
);

-- 創建營業時間表
CREATE TABLE public.business_hours (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  shop_id uuid NOT NULL REFERENCES public.shops(id) ON DELETE CASCADE,
  day_of_week integer NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 1=Monday, etc.
  is_open boolean NOT NULL DEFAULT true,
  open_time time NOT NULL DEFAULT '09:00',
  close_time time NOT NULL DEFAULT '18:00',
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(shop_id, day_of_week)
);

-- 創建預約時段模板表
CREATE TABLE public.time_slots (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  shop_id uuid NOT NULL REFERENCES public.shops(id) ON DELETE CASCADE,
  start_time time NOT NULL,
  duration_minutes integer NOT NULL DEFAULT 60,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- 創建票券表
CREATE TABLE public.coupons (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  shop_id uuid NOT NULL REFERENCES public.shops(id) ON DELETE CASCADE,
  code text NOT NULL,
  name text NOT NULL,
  description text,
  type text NOT NULL CHECK (type IN ('percentage', 'fixed', 'free_service')),
  value integer NOT NULL DEFAULT 0,
  min_amount integer NOT NULL DEFAULT 0,
  max_discount integer,
  valid_from date NOT NULL,
  valid_to date NOT NULL,
  usage_limit integer NOT NULL DEFAULT 1,
  used_count integer NOT NULL DEFAULT 0,
  is_active boolean NOT NULL DEFAULT true,
  applicable_services text[] DEFAULT ARRAY['all'],
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(shop_id, code)
);

-- 創建用戶票券關聯表
CREATE TABLE public.user_coupons (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL,
  coupon_id uuid NOT NULL REFERENCES public.coupons(id) ON DELETE CASCADE,
  used_at timestamp with time zone,
  booking_id uuid REFERENCES public.bookings(id),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(user_id, coupon_id)
);

-- 啟用 RLS
ALTER TABLE public.line_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_hours ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_coupons ENABLE ROW LEVEL SECURITY;

-- 創建管理員角色表
CREATE TABLE public.admin_users (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL UNIQUE,
  shop_id uuid NOT NULL REFERENCES public.shops(id) ON DELETE CASCADE,
  role text NOT NULL DEFAULT 'admin' CHECK (role IN ('admin', 'manager')),
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

-- 創建檢查管理員權限的函數
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid, target_shop_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.admin_users
    WHERE admin_users.user_id = is_admin.user_id
    AND admin_users.shop_id = is_admin.target_shop_id
    AND admin_users.is_active = true
  )
$$;

-- LINE 設定 RLS 策略
CREATE POLICY "管理員可以查看店家 LINE 設定"
ON public.line_settings
FOR SELECT
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以插入店家 LINE 設定"
ON public.line_settings
FOR INSERT
WITH CHECK (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新店家 LINE 設定"
ON public.line_settings
FOR UPDATE
USING (public.is_admin(auth.uid(), shop_id));

-- 營業時間 RLS 策略
CREATE POLICY "管理員可以查看店家營業時間"
ON public.business_hours
FOR SELECT
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以插入店家營業時間"
ON public.business_hours
FOR INSERT
WITH CHECK (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新店家營業時間"
ON public.business_hours
FOR UPDATE
USING (public.is_admin(auth.uid(), shop_id));

-- 時段 RLS 策略
CREATE POLICY "管理員可以查看店家時段"
ON public.time_slots
FOR SELECT
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以插入店家時段"
ON public.time_slots
FOR INSERT
WITH CHECK (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新店家時段"
ON public.time_slots
FOR UPDATE
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以刪除店家時段"
ON public.time_slots
FOR DELETE
USING (public.is_admin(auth.uid(), shop_id));

-- 票券 RLS 策略
CREATE POLICY "所有人可以查看啟用的票券"
ON public.coupons
FOR SELECT
USING (is_active = true);

CREATE POLICY "管理員可以查看所有店家票券"
ON public.coupons
FOR SELECT
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以插入店家票券"
ON public.coupons
FOR INSERT
WITH CHECK (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新店家票券"
ON public.coupons
FOR UPDATE
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以刪除店家票券"
ON public.coupons
FOR DELETE
USING (public.is_admin(auth.uid(), shop_id));

-- 用戶票券 RLS 策略
CREATE POLICY "用戶可以查看自己的票券"
ON public.user_coupons
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "用戶可以使用自己的票券"
ON public.user_coupons
FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "系統可以分配票券給用戶"
ON public.user_coupons
FOR INSERT
WITH CHECK (true);

-- 管理員用戶 RLS 策略
CREATE POLICY "管理員可以查看同店家管理員"
ON public.admin_users
FOR SELECT
USING (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以新增同店家管理員"
ON public.admin_users
FOR INSERT
WITH CHECK (public.is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新同店家管理員"
ON public.admin_users
FOR UPDATE
USING (public.is_admin(auth.uid(), shop_id));

-- 為更新時間創建觸發器
CREATE TRIGGER update_line_settings_updated_at
BEFORE UPDATE ON public.line_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_business_hours_updated_at
BEFORE UPDATE ON public.business_hours
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_time_slots_updated_at
BEFORE UPDATE ON public.time_slots
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_coupons_updated_at
BEFORE UPDATE ON public.coupons
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at
BEFORE UPDATE ON public.admin_users
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- 插入測試數據
INSERT INTO public.shops (name, description, is_active) VALUES
('測試美容院', '提供專業美容美髮服務', true);

-- 假設第一個店家 ID (需要根據實際情況調整)
DO $$
DECLARE
    shop_uuid uuid;
BEGIN
    SELECT id INTO shop_uuid FROM public.shops WHERE name = '測試美容院' LIMIT 1;
    
    -- 插入營業時間
    INSERT INTO public.business_hours (shop_id, day_of_week, is_open, open_time, close_time) VALUES
    (shop_uuid, 1, true, '09:00', '18:00'), -- Monday
    (shop_uuid, 2, true, '09:00', '18:00'), -- Tuesday
    (shop_uuid, 3, true, '09:00', '18:00'), -- Wednesday
    (shop_uuid, 4, true, '09:00', '18:00'), -- Thursday
    (shop_uuid, 5, true, '09:00', '18:00'), -- Friday
    (shop_uuid, 6, true, '10:00', '17:00'), -- Saturday
    (shop_uuid, 0, false, '10:00', '17:00'); -- Sunday
    
    -- 插入時段
    INSERT INTO public.time_slots (shop_id, start_time, duration_minutes) VALUES
    (shop_uuid, '09:00', 60),
    (shop_uuid, '10:00', 60),
    (shop_uuid, '11:00', 60),
    (shop_uuid, '14:00', 60),
    (shop_uuid, '15:00', 60),
    (shop_uuid, '16:00', 60);
END $$;