# Edge Function 問題排除指南

## 🚨 FunctionsFetchError 錯誤

當您看到 `FunctionsFetchError` 錯誤時，這表示無法連接到 Supabase Edge Function。

### 📋 可能原因

1. **網路連線問題**
   - 防火牆阻擋
   - 網路代理設定
   - DNS 解析問題

2. **Supabase 設定問題**
   - 專案 URL 錯誤
   - API Key 錯誤
   - Edge Function 未部署

3. **瀏覽器問題**
   - CORS 限制
   - 瀏覽器擴充功能阻擋
   - 快取問題

### 🔧 診斷步驟

#### 步驟 1：使用診斷工具
1. 前往 [診斷頁面](https://line-beauty-appoint.lovable.app/admin/line-test)
2. 查看 "Edge Function 連線測試" 區塊
3. 點擊 "生成詳細報告" 查看完整診斷

#### 步驟 2：檢查瀏覽器控制台
1. 按 F12 開啟開發者工具
2. 切換到 Network 頁籤
3. 嘗試觸發 Edge Function 調用
4. 查看是否有失敗的請求

#### 步驟 3：手動測試連線
在瀏覽器控制台執行：
```javascript
fetch('https://jyehyqcjusiywggofpld.supabase.co/functions/v1/line-login-token', {
  method: 'OPTIONS',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp5ZWh5cWNqdXNpeXdnZ29mcGxkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2ODg4MjAsImV4cCI6MjA2NzI2NDgyMH0.AC-58WjFXUwRJ98zAN3W--t1JCSgmv5cy7KJBCJSBj0'
  }
}).then(r => console.log('連線成功:', r.status)).catch(e => console.error('連線失敗:', e));
```

### 🛠️ 解決方案

#### 方案 1：檢查網路連線
```
1. 確認可以訪問其他網站
2. 嘗試關閉 VPN 或代理
3. 檢查防火牆設定
4. 嘗試使用不同的網路環境
```

#### 方案 2：清除瀏覽器快取
```
1. 按 Ctrl+Shift+Delete (Windows) 或 Cmd+Shift+Delete (Mac)
2. 選擇清除快取和 Cookie
3. 重新載入頁面
```

#### 方案 3：檢查瀏覽器設定
```
1. 暫時停用瀏覽器擴充功能
2. 嘗試使用無痕模式
3. 檢查是否有廣告阻擋器干擾
```

#### 方案 4：驗證 Supabase 設定
```
1. 確認專案 URL: https://jyehyqcjusiywggofpld.supabase.co
2. 確認 API Key 正確
3. 檢查 Edge Function 部署狀態
```

### 📊 常見錯誤代碼

| 錯誤 | 原因 | 解決方法 |
|------|------|----------|
| `net::ERR_NETWORK_CHANGED` | 網路連線變更 | 重新連線網路 |
| `net::ERR_INTERNET_DISCONNECTED` | 網路斷線 | 檢查網路連線 |
| `net::ERR_BLOCKED_BY_CLIENT` | 瀏覽器阻擋 | 檢查擴充功能 |
| `CORS error` | 跨域請求被阻擋 | 檢查 Edge Function CORS 設定 |
| `404 Not Found` | Edge Function 不存在 | 確認 Function 已部署 |

### 🔍 進階診斷

#### 檢查 Edge Function 狀態
如果您有 Supabase 專案管理權限：
1. 前往 [Supabase Dashboard](https://supabase.com/dashboard)
2. 選擇專案 `jyehyqcjusiywggofpld`
3. 前往 Edge Functions 頁面
4. 確認 `line-login-token` 函數狀態為 ACTIVE

#### 檢查 Function 日誌
1. 在 Supabase Dashboard 中查看 Function 執行日誌
2. 尋找錯誤訊息或異常
3. 檢查是否有部署問題

#### 測試其他 Edge Function
創建一個簡單的測試 Function：
```typescript
serve((req) => {
  return new Response(JSON.stringify({ message: "Hello World" }), {
    headers: { "Content-Type": "application/json" },
  });
});
```

### 🆘 仍然無法解決？

#### 收集診斷資訊
1. 瀏覽器控制台的完整錯誤訊息
2. Network 頁籤中的失敗請求詳情
3. 診斷頁面的完整報告
4. 作業系統和瀏覽器版本

#### 替代方案
如果 Edge Function 持續無法使用，可以考慮：
1. 使用直接的 LINE API 調用（需要處理 CORS）
2. 設置自己的後端服務
3. 使用其他雲端 Function 服務

#### 聯繫支援
提供以下資訊：
- 錯誤發生的具體時間
- 使用的瀏覽器和版本
- 網路環境（家用/公司/行動網路）
- 診斷報告的完整內容

### 💡 預防措施

1. **定期檢查**：每週檢查一次 Edge Function 狀態
2. **監控設定**：設置 Edge Function 監控告警
3. **備用方案**：準備替代的 API 調用方式
4. **文檔更新**：記錄所有設定變更

### 🔗 相關資源

- [Supabase Edge Functions 文檔](https://supabase.com/docs/guides/functions)
- [Supabase 狀態頁面](https://status.supabase.com/)
- [瀏覽器網路問題排除](https://support.google.com/chrome/answer/95669)

---

**記住**：大多數 Edge Function 連線問題都是暫時性的網路問題。如果診斷工具顯示連線正常，問題通常會自動解決。
