import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { debugLineLogin, validateRedirectUri, type LineLoginDebugInfo } from "@/utils/lineLoginDebug";
import { runLineLoginDiagnostic, type LineLoginDiagnostic } from "@/utils/lineLoginDiagnostic";
import { testEdgeFunctionConnection, generateDiagnosticReport, type EdgeFunctionTestResult } from "@/utils/edgeFunctionTest";
import { runStateValidationDiagnostic, autoFixStateIssues, type StateValidationResult } from "@/utils/stateValidationDiagnostic";
import { runEmailDiagnostic, autoFixEmailIssues, type EmailDiagnosticResult } from "@/utils/emailValidationDiagnostic";
import { supabase } from "@/integrations/supabase/client";

const LineLoginTest = () => {
  const navigate = useNavigate();
  const [debugInfo, setDebugInfo] = useState<LineLoginDebugInfo | null>(null);
  const [diagnostic, setDiagnostic] = useState<LineLoginDiagnostic | null>(null);
  const [edgeFunctionTest, setEdgeFunctionTest] = useState<EdgeFunctionTestResult[] | null>(null);
  const [stateValidation, setStateValidation] = useState<{results: StateValidationResult[], canProceed: boolean, recommendations: string[]} | null>(null);
  const [emailDiagnostic, setEmailDiagnostic] = useState<{results: EmailDiagnosticResult[], hasIssues: boolean, recommendations: string[]} | null>(null);
  const [lineSettings, setLineSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDebugInfo();
  }, []);

  const loadDebugInfo = async () => {
    setLoading(true);
    try {
      // 載入除錯資訊
      const info = await debugLineLogin();

      // 執行完整診斷
      const diagnosticResult = await runLineLoginDiagnostic();

      // 執行 Edge Function 連線測試
      const edgeFunctionResults = await testEdgeFunctionConnection();

      // 執行 State 驗證診斷
      const stateValidationResult = runStateValidationDiagnostic();

      // 執行 Email 驗證診斷（使用測試 LINE User ID）
      const testLineUserId = "udc45e0923dbafa371c0d88af25ece0b4";
      const emailDiagnosticResult = runEmailDiagnostic(testLineUserId);

      // 載入 LINE 設定
      const { data: settings } = await supabase
        .from('line_settings')
        .select('*')
        .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
        .single();

      if (settings) {
        info.currentRedirectUri = settings.redirect_uri;
        info.channelId = settings.channel_id;
        
        // 驗證 redirect_uri
        if (settings.redirect_uri) {
          const validation = validateRedirectUri(settings.redirect_uri);
          if (!validation.valid) {
            info.issues.push(...validation.errors);
          }
        } else {
          info.issues.push('Redirect URI 未設定');
        }

        // 檢查 Channel ID
        if (!settings.channel_id) {
          info.issues.push('Channel ID 未設定');
        }

        // 檢查 Channel Secret
        if (!settings.channel_secret) {
          info.issues.push('Channel Secret 未設定');
        }
      } else {
        info.issues.push('LINE 設定不存在');
      }

      setDebugInfo(info);
      setDiagnostic(diagnosticResult);
      setEdgeFunctionTest(edgeFunctionResults);
      setStateValidation(stateValidationResult);
      setEmailDiagnostic(emailDiagnosticResult);
      setLineSettings(settings);
    } catch (error) {
      console.error('載入除錯資訊失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (hasIssues: boolean) => {
    if (hasIssues) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
    return <CheckCircle className="h-5 w-5 text-green-500" />;
  };

  const getStatusColor = (hasIssues: boolean) => {
    return hasIssues ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">載入中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin/line-settings')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回設定
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">LINE Login 診斷</h1>
            <p className="text-gray-600">檢查 LINE Login 設定狀態</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* 整體狀態 */}
          <Card className={`border-2 ${getStatusColor(debugInfo?.issues.length > 0)}`}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(debugInfo?.issues.length > 0)}
                整體狀態
              </CardTitle>
            </CardHeader>
            <CardContent>
              {debugInfo?.issues.length === 0 ? (
                <p className="text-green-700">✅ LINE Login 設定看起來正常</p>
              ) : (
                <p className="text-red-700">❌ 發現 {debugInfo?.issues.length} 個問題需要修正</p>
              )}
            </CardContent>
          </Card>

          {/* 環境資訊 */}
          <Card>
            <CardHeader>
              <CardTitle>環境資訊</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <strong>當前 Origin:</strong>
                  <p className="font-mono text-sm bg-gray-100 p-2 rounded">{debugInfo?.currentOrigin}</p>
                </div>
                <div>
                  <strong>建議的 Redirect URI:</strong>
                  <p className="font-mono text-sm bg-gray-100 p-2 rounded">{debugInfo?.expectedRedirectUri}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 設定狀態 */}
          <Card>
            <CardHeader>
              <CardTitle>設定狀態</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  {getStatusIcon(!lineSettings?.channel_id)}
                  <span>Channel ID: {lineSettings?.channel_id ? '已設定' : '未設定'}</span>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(!lineSettings?.channel_secret)}
                  <span>Channel Secret: {lineSettings?.channel_secret ? '已設定' : '未設定'}</span>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(!lineSettings?.redirect_uri)}
                  <span>Redirect URI: {lineSettings?.redirect_uri || '未設定'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 問題列表 */}
          {debugInfo?.issues.length > 0 && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="h-5 w-5" />
                  發現的問題
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {debugInfo.issues.map((issue, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <XCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <span className="text-red-700">{issue}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Email 驗證診斷 */}
          {emailDiagnostic && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Email 格式驗證診斷
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className={`p-3 rounded-lg border-2 ${
                  !emailDiagnostic.hasIssues ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {!emailDiagnostic.hasIssues ?
                      <CheckCircle className="h-5 w-5 text-green-500" /> :
                      <XCircle className="h-5 w-5 text-orange-500" />
                    }
                    <span className={`font-medium ${
                      !emailDiagnostic.hasIssues ? 'text-green-700' : 'text-orange-700'
                    }`}>
                      {!emailDiagnostic.hasIssues ? 'Email 格式驗證正常' : 'Email 格式有問題'}
                    </span>
                  </div>
                  {emailDiagnostic.hasIssues && (
                    <button
                      onClick={() => {
                        const testLineUserId = "udc45e0923dbafa371c0d88af25ece0b4";
                        const fixResult = autoFixEmailIssues(testLineUserId);
                        if (fixResult.success) {
                          alert(`已生成新的有效 Email: ${fixResult.newEmail}`);
                        } else {
                          alert(`自動修復失敗: ${fixResult.error}`);
                        }
                      }}
                      className="mt-2 px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
                    >
                      🔧 自動修復 Email
                    </button>
                  )}
                </div>

                {emailDiagnostic.results.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    {result.success ?
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" /> :
                      <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                    }
                    <div className="flex-1">
                      <div className="font-medium">{result.step}</div>
                      <div className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                        {result.message}
                      </div>
                      {result.recommendation && (
                        <div className="text-xs text-orange-600 mt-1">
                          💡 {result.recommendation}
                        </div>
                      )}
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">詳細資訊</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}

                {emailDiagnostic.recommendations.length > 0 && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">修復建議</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      {emailDiagnostic.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-blue-500">•</span>
                          <span>{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* State 驗證診斷 */}
          {stateValidation && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  安全驗證 (State) 診斷
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className={`p-3 rounded-lg border-2 ${
                  stateValidation.canProceed ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {stateValidation.canProceed ?
                      <CheckCircle className="h-5 w-5 text-green-500" /> :
                      <XCircle className="h-5 w-5 text-red-500" />
                    }
                    <span className={`font-medium ${
                      stateValidation.canProceed ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {stateValidation.canProceed ? '安全驗證正常' : '安全驗證有問題'}
                    </span>
                  </div>
                  {!stateValidation.canProceed && (
                    <button
                      onClick={() => {
                        const success = autoFixStateIssues();
                        if (success) {
                          alert("已自動清除問題狀態，請重新載入頁面");
                          window.location.reload();
                        } else {
                          alert("自動修復失敗，請手動清除瀏覽器快取");
                        }
                      }}
                      className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                    >
                      🔧 自動修復
                    </button>
                  )}
                </div>

                {stateValidation.results.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    {result.success ?
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" /> :
                      <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                    }
                    <div className="flex-1">
                      <div className="font-medium">{result.step}</div>
                      <div className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                        {result.message}
                      </div>
                      {result.recommendation && (
                        <div className="text-xs text-orange-600 mt-1">
                          💡 {result.recommendation}
                        </div>
                      )}
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">詳細資訊</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}

                {stateValidation.recommendations.length > 0 && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 className="font-medium text-yellow-800 mb-2">修復建議</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      {stateValidation.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-yellow-500">•</span>
                          <span>{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Edge Function 連線測試 */}
          {edgeFunctionTest && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Edge Function 連線測試
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {edgeFunctionTest.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    {result.success ?
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" /> :
                      <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                    }
                    <div className="flex-1">
                      <div className="font-medium">{result.step}</div>
                      <div className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                        {result.message}
                      </div>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">詳細資訊</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}

                {/* 診斷報告按鈕 */}
                <div className="pt-4 border-t">
                  <button
                    onClick={() => {
                      const report = generateDiagnosticReport(edgeFunctionTest);
                      console.log(report);
                      alert('診斷報告已輸出到瀏覽器控制台，請按 F12 查看');
                    }}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    生成詳細報告
                  </button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 詳細診斷結果 */}
          {diagnostic && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  系統診斷結果
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {diagnostic.results.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    {result.status === 'success' && <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />}
                    {result.status === 'error' && <XCircle className="h-5 w-5 text-red-500 mt-0.5" />}
                    {result.status === 'warning' && <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />}
                    <div className="flex-1">
                      <div className="font-medium">{result.step}</div>
                      <div className={`text-sm ${
                        result.status === 'success' ? 'text-green-700' :
                        result.status === 'error' ? 'text-red-700' : 'text-yellow-700'
                      }`}>
                        {result.message}
                      </div>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">詳細資訊</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* 修復建議 */}
          {diagnostic?.recommendations.length > 0 && (
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <AlertCircle className="h-5 w-5" />
                  修復建議
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {diagnostic.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      <span className="text-blue-700">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* 建議 */}
          {debugInfo?.suggestions.length > 0 && (
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <AlertCircle className="h-5 w-5" />
                  環境建議
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {debugInfo.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      <span className="text-blue-700">{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* 操作按鈕 */}
          <div className="flex gap-4">
            <Button onClick={loadDebugInfo} variant="outline">
              重新檢查
            </Button>
            <Button onClick={() => navigate('/admin/line-settings')}>
              前往設定頁面
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LineLoginTest;
