import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowLeft, MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { generateEnhancedState } from "@/utils/stateValidationDiagnostic";

const Login = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  // LINE登入功能
  const handleLineLogin = async () => {
    setLoading(true);
    try {
      // 從資料庫獲取 LINE 設定
      const { data: lineSettings, error } = await supabase
        .from('line_settings')
        .select('channel_id, redirect_uri')
        .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
        .single();

      if (error || !lineSettings?.channel_id) {
        toast({
          title: "LINE 登入設定錯誤",
          description: "請聯繫管理員設定 LINE Login 參數",
          variant: "destructive",
        });
        return;
      }

      // 產生增強的 state 參數（包含時間戳）用於安全驗證
      const state = generateEnhancedState();

      // 清除舊的 state 並設定新的
      localStorage.removeItem('line_login_state');
      localStorage.setItem('line_login_state', state);

      console.log('🔐 生成新的 LINE Login state:', state.substring(0, 8) + '...');

      // 建構 LINE Login URL
      const lineLoginUrl = new URL('https://access.line.me/oauth2/v2.1/authorize');
      lineLoginUrl.searchParams.append('response_type', 'code');
      lineLoginUrl.searchParams.append('client_id', lineSettings.channel_id);
      lineLoginUrl.searchParams.append('redirect_uri', lineSettings.redirect_uri || `${window.location.origin}/auth/line/callback`);
      lineLoginUrl.searchParams.append('state', state);
      lineLoginUrl.searchParams.append('scope', 'profile openid email');

      // 導向 LINE Login 頁面
      window.location.href = lineLoginUrl.toString();
    } catch (error) {
      console.error('LINE login error:', error);
      toast({
        title: "LINE 登入失敗",
        description: "發生未預期的錯誤，請稍後再試",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-soft to-background flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => navigate(-1)}
            className="rounded-full"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-2xl font-bold text-foreground">登入</h1>
          <div className="w-10" />
        </div>

        {/* Login Card */}
        <Card className="p-6 card-shadow">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-primary rounded-full flex items-center justify-center">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800">使用 LINE 帳號登入</h2>
            <p className="text-muted-foreground text-sm">
              快速安全的登入方式
            </p>
            <Button
              onClick={handleLineLogin}
              className="w-full h-12 beauty-gradient text-white font-semibold rounded-full"
              disabled={loading}
            >
              <MessageCircle className="w-5 h-5 mr-2" />
              {loading ? "處理中..." : "使用 LINE 登入"}
            </Button>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center space-y-2">
          <p className="text-xs text-muted-foreground">
            繼續即表示您同意我們的服務條款和隱私政策
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;