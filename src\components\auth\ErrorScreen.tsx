import { useNavigate } from "react-router-dom";
import { StateErrorHandler } from "./StateErrorHandler";

interface ErrorScreenProps {
  error: string;
  receivedState?: string;
  savedState?: string | null;
  onRetry?: () => void;
}

export const ErrorScreen = ({ error, receivedState, savedState, onRetry }: ErrorScreenProps) => {
  const navigate = useNavigate();

  // 如果是 state 驗證失敗，使用專門的錯誤處理器
  if (error.includes("安全驗證失敗") || error.includes("State parameter mismatch")) {
    return (
      <StateErrorHandler
        receivedState={receivedState}
        savedState={savedState}
        onRetry={onRetry}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-pink-soft to-background p-4">
      <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
        <h2 className="text-xl font-semibold text-red-600 mb-4">登入失敗</h2>
        <p className="text-gray-700 mb-4">{error}</p>

        {error.includes("無法獲取 LINE 授權") && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700 mb-2">
              這個問題通常是由於 LINE 設定不正確或 Edge Function 連線問題造成的。
            </p>
            <div className="space-y-1">
              <button
                onClick={() => navigate("/admin/line-test")}
                className="block text-sm text-blue-600 underline hover:text-blue-800"
              >
                前往診斷頁面查看詳細問題
              </button>
              <button
                onClick={() => {
                  console.group("🔧 快速診斷");
                  console.log("錯誤訊息:", error);
                  console.log("時間:", new Date().toISOString());
                  console.log("瀏覽器:", navigator.userAgent);
                  console.log("網址:", window.location.href);
                  console.groupEnd();
                  alert("診斷資訊已輸出到控制台，請按 F12 查看");
                }}
                className="block text-sm text-blue-600 underline hover:text-blue-800"
              >
                輸出診斷資訊到控制台
              </button>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <button
            onClick={() => navigate("/login")}
            className="w-full py-2 px-4 bg-primary text-white rounded-full hover:bg-primary/90 transition-colors"
          >
            返回登入頁面
          </button>
          <button
            onClick={() => navigate("/admin/line-settings")}
            className="w-full py-2 px-4 bg-gray-200 text-gray-700 rounded-full hover:bg-gray-300 transition-colors"
          >
            檢查 LINE 設定
          </button>
        </div>
      </div>
    </div>
  );
};