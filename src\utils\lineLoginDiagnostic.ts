/**
 * LINE Login 診斷工具
 * 幫助診斷「無法獲取 LINE 授權」錯誤的具體原因
 */

import { supabase } from "@/integrations/supabase/client";

export interface DiagnosticResult {
  step: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

export interface LineLoginDiagnostic {
  results: DiagnosticResult[];
  overallStatus: 'success' | 'error' | 'warning';
  recommendations: string[];
}

/**
 * 執行完整的 LINE Login 診斷
 */
export const runLineLoginDiagnostic = async (): Promise<LineLoginDiagnostic> => {
  const results: DiagnosticResult[] = [];
  const recommendations: string[] = [];

  // 1. 檢查 LINE 設定
  try {
    const { data: lineSettings, error: settingsError } = await supabase
      .from("line_settings")
      .select("channel_id, channel_secret, redirect_uri")
      .eq("shop_id", "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa")
      .single();

    if (settingsError || !lineSettings) {
      results.push({
        step: "LINE 設定檢查",
        status: "error",
        message: "無法載入 LINE 設定",
        details: settingsError
      });
      recommendations.push("請前往 /admin/line-settings 設定 LINE API 參數");
    } else {
      results.push({
        step: "LINE 設定檢查",
        status: "success",
        message: "LINE 設定載入成功"
      });

      // 檢查各項設定
      if (!lineSettings.channel_id) {
        results.push({
          step: "Channel ID 檢查",
          status: "error",
          message: "Channel ID 未設定"
        });
        recommendations.push("請在 LINE Settings 中填入 Channel ID");
      } else {
        results.push({
          step: "Channel ID 檢查",
          status: "success",
          message: `Channel ID 已設定 (${lineSettings.channel_id.substring(0, 10)}...)`
        });
      }

      if (!lineSettings.channel_secret) {
        results.push({
          step: "Channel Secret 檢查",
          status: "error",
          message: "Channel Secret 未設定"
        });
        recommendations.push("請在 LINE Settings 中填入 Channel Secret");
      } else {
        results.push({
          step: "Channel Secret 檢查",
          status: "success",
          message: "Channel Secret 已設定"
        });
      }

      if (!lineSettings.redirect_uri) {
        results.push({
          step: "Redirect URI 檢查",
          status: "error",
          message: "Redirect URI 未設定"
        });
        recommendations.push("請在 LINE Settings 中設定 Redirect URI");
      } else {
        const expectedUri = `${window.location.origin}/auth/line/callback`;
        if (lineSettings.redirect_uri !== expectedUri) {
          results.push({
            step: "Redirect URI 檢查",
            status: "warning",
            message: `Redirect URI 可能不正確: ${lineSettings.redirect_uri}`,
            details: { expected: expectedUri, actual: lineSettings.redirect_uri }
          });
          recommendations.push("請檢查 Redirect URI 是否與當前環境匹配");
        } else {
          results.push({
            step: "Redirect URI 檢查",
            status: "success",
            message: "Redirect URI 設定正確"
          });
        }
      }
    }
  } catch (error) {
    results.push({
      step: "LINE 設定檢查",
      status: "error",
      message: "檢查 LINE 設定時發生錯誤",
      details: error
    });
  }

  // 2. 檢查 Supabase Edge Function
  try {
    // 先檢查 Edge Function 是否可以訪問（使用 OPTIONS 請求）
    const supabaseUrl = "https://jyehyqcjusiywggofpld.supabase.co";
    const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp5ZWh5cWNqdXNpeXdnZ29mcGxkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2ODg4MjAsImV4cCI6MjA2NzI2NDgyMH0.AC-58WjFXUwRJ98zAN3W--t1JCSgmv5cy7KJBCJSBj0";

    const testResponse = await fetch(`${supabaseUrl}/functions/v1/line-login-token`, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!testResponse.ok) {
      results.push({
        step: "Edge Function 檢查",
        status: "error",
        message: "Edge Function 端點無法訪問",
        details: {
          status: testResponse.status,
          statusText: testResponse.statusText,
          url: `${supabaseUrl}/functions/v1/line-login-token`
        }
      });
      recommendations.push("請檢查 Supabase Edge Function 是否正確部署");
      recommendations.push("請確認 Supabase 專案設定正確");
    } else {
      // 如果 OPTIONS 成功，再測試實際調用
      const testPayload = {
        code: "test_code",
        redirectUri: `${window.location.origin}/auth/line/callback`,
        channelId: "test_channel_id",
        channelSecret: "test_channel_secret"
      };

      const { data, error: functionError } = await supabase.functions.invoke(
        "line-login-token",
        { body: testPayload }
      );

      if (functionError) {
        results.push({
          step: "Edge Function 檢查",
          status: "error",
          message: "Edge Function 調用失敗",
          details: functionError
        });

        // 根據錯誤類型提供具體建議
        if (functionError.name === 'FunctionsFetchError') {
          recommendations.push("網路連線問題：請檢查網路連線");
          recommendations.push("請確認 Supabase 專案 URL 和 API Key 正確");
        } else {
          recommendations.push("請檢查 Edge Function 程式碼是否有錯誤");
        }
      } else {
        results.push({
          step: "Edge Function 檢查",
          status: "success",
          message: "Edge Function 可以正常調用"
        });
      }
    }
  } catch (error) {
    results.push({
      step: "Edge Function 檢查",
      status: "error",
      message: "測試 Edge Function 時發生錯誤",
      details: error
    });
    recommendations.push("請檢查網路連線和 Supabase 設定");
    recommendations.push("請確認瀏覽器沒有阻擋跨域請求");
  }

  // 3. 檢查環境設定
  const currentOrigin = window.location.origin;
  if (currentOrigin.includes('localhost')) {
    results.push({
      step: "環境檢查",
      status: "warning",
      message: "當前為開發環境",
      details: { origin: currentOrigin }
    });
    recommendations.push("請確保在 LINE Developers Console 中註冊了 localhost callback URL");
  } else if (currentOrigin.includes('lovable.app')) {
    results.push({
      step: "環境檢查",
      status: "success",
      message: "當前為 Lovable 生產環境",
      details: { origin: currentOrigin }
    });
  } else {
    results.push({
      step: "環境檢查",
      status: "warning",
      message: "未知的部署環境",
      details: { origin: currentOrigin }
    });
  }

  // 4. 檢查瀏覽器狀態
  const hasState = localStorage.getItem('line_login_state');
  if (hasState) {
    results.push({
      step: "瀏覽器狀態檢查",
      status: "success",
      message: "瀏覽器中有 LINE 登入狀態"
    });
  } else {
    results.push({
      step: "瀏覽器狀態檢查",
      status: "warning",
      message: "瀏覽器中沒有 LINE 登入狀態"
    });
  }

  // 判斷整體狀態
  const hasErrors = results.some(r => r.status === 'error');
  const hasWarnings = results.some(r => r.status === 'warning');
  
  let overallStatus: 'success' | 'error' | 'warning' = 'success';
  if (hasErrors) {
    overallStatus = 'error';
  } else if (hasWarnings) {
    overallStatus = 'warning';
  }

  return {
    results,
    overallStatus,
    recommendations
  };
};

/**
 * 測試 LINE token 交換
 */
export const testLineTokenExchange = async (code: string): Promise<DiagnosticResult> => {
  try {
    const { data: lineSettings } = await supabase
      .from("line_settings")
      .select("channel_id, channel_secret, redirect_uri")
      .eq("shop_id", "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa")
      .single();

    if (!lineSettings) {
      return {
        step: "Token 交換測試",
        status: "error",
        message: "無法載入 LINE 設定"
      };
    }

    const { data, error } = await supabase.functions.invoke(
      "line-login-token",
      {
        body: {
          code,
          redirectUri: lineSettings.redirect_uri,
          channelId: lineSettings.channel_id,
          channelSecret: lineSettings.channel_secret,
        },
      }
    );

    if (error) {
      return {
        step: "Token 交換測試",
        status: "error",
        message: "Token 交換失敗",
        details: error
      };
    }

    return {
      step: "Token 交換測試",
      status: "success",
      message: "Token 交換成功",
      details: data
    };
  } catch (error) {
    return {
      step: "Token 交換測試",
      status: "error",
      message: "Token 交換過程發生錯誤",
      details: error
    };
  }
};
