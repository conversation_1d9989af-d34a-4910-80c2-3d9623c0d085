import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  User, 
  Clock, 
  Calendar, 
  DollarSign, 
  Ticket,
  ChevronRight,
  MapPin
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

interface Service {
  id: string;
  name: string;
  description: string | null;
  duration_minutes: number;
  price_min: number;
  price_max: number | null;
}

interface Coupon {
  id: string;
  name: string;
  code: string;
  type: string;
  value: number;
  min_amount: number;
  max_discount?: number;
  usage_limit: number;
  used_count: number;
  valid_from: string;
  valid_to: string;
}

const BookingConfirm = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [couponCode, setCouponCode] = useState("");
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [notes, setNotes] = useState("");
  const [services, setServices] = useState<Service[]>([]);
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const [totalPrice, setTotalPrice] = useState(0);
  const [finalPrice, setFinalPrice] = useState(0);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // 檢查登入狀態和載入預約資料
  useEffect(() => {
    if (!user) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行預約",
        variant: "destructive",
      });
      navigate("/login");
      return;
    }

    // 載入儲存的預約資料
    const storedServices = localStorage.getItem('selectedServices');
    const storedDate = localStorage.getItem('selectedDate');
    const storedTime = localStorage.getItem('selectedTime');
    const storedPrice = localStorage.getItem('totalPrice');

    if (!storedServices || !storedDate || !storedTime) {
      toast({
        title: "預約資料遺失",
        description: "請重新選擇服務和時間",
        variant: "destructive",
      });
      navigate("/services");
      return;
    }

    setSelectedDate(storedDate);
    setSelectedTime(storedTime);
    setTotalPrice(parseInt(storedPrice || "0"));
    setFinalPrice(parseInt(storedPrice || "0"));

    // 載入服務詳情
    const fetchServices = async () => {
      try {
        const serviceIds = JSON.parse(storedServices);
        const { data, error } = await supabase
          .from('services')
          .select('*')
          .in('id', serviceIds);

        if (error) throw error;

        setServices(data || []);
      } catch (error: any) {
        toast({
          title: "載入服務資料失敗",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [user, navigate]);

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) return;

    try {
      const { data, error } = await supabase
        .from('coupons')
        .select('*')
        .eq('code', couponCode.trim())
        .eq('shop_id', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
        .eq('is_active', true)
        .single();

      if (error) {
        toast({
          title: "票券無效",
          description: "找不到此票券代碼或票券已失效",
          variant: "destructive",
        });
        return;
      }

      // 檢查最低消費金額
      if (totalPrice < data.min_amount) {
        toast({
          title: "不符合使用條件",
          description: `此票券需消費滿 ${data.min_amount} 元才能使用`,
          variant: "destructive",
        });
        return;
      }

      // 檢查使用期限
      const now = new Date();
      const validFrom = new Date(data.valid_from);
      const validTo = new Date(data.valid_to);
      if (now < validFrom || now > validTo) {
        toast({
          title: "票券已過期",
          description: "此票券已過使用期限",
          variant: "destructive",
        });
        return;
      }

      // 檢查使用次數
      if (data.used_count >= data.usage_limit) {
        toast({
          title: "票券已用完",
          description: "此票券已達使用上限",
          variant: "destructive",
        });
        return;
      }

      setAppliedCoupon(data);
      
      // 計算折扣後價格
      let discount = 0;
      if (data.type === 'fixed') {
        discount = data.value;
      } else if (data.type === 'percentage') {
        discount = Math.floor(totalPrice * data.value / 100);
        if (data.max_discount && discount > data.max_discount) {
          discount = data.max_discount;
        }
      }
      
      setFinalPrice(Math.max(0, totalPrice - discount));
      
      toast({
        title: "票券套用成功",
        description: `已享有 ${discount} 元折扣`,
      });
    } catch (error: any) {
      toast({
        title: "套用票券失敗",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleConfirmBooking = async () => {
    if (!user) return;

    setSubmitting(true);
    try {
      // 建立預約記錄
      const bookingData = {
        user_id: user.id,
        shop_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        service_id: services[0]?.id, // 使用第一個服務的ID
        booking_date: selectedDate,
        booking_time: selectedTime,
        estimated_price: finalPrice,
        notes: notes || null,
        status: 'pending'
      };

      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .insert(bookingData)
        .select()
        .single();

      if (bookingError) throw bookingError;

      // 如果使用了票券，記錄使用記錄
      if (appliedCoupon) {
        const { error: couponError } = await supabase
          .from('user_coupons')
          .insert({
            user_id: user.id,
            coupon_id: appliedCoupon.id,
            booking_id: booking.id,
            used_at: new Date().toISOString()
          });

        if (couponError) {
          console.error('記錄票券使用失敗:', couponError);
        }

        // 更新票券使用次數
        await supabase
          .from('coupons')
          .update({ used_count: appliedCoupon.used_count + 1 })
          .eq('id', appliedCoupon.id);
      }

      // 清除暫存資料
      localStorage.removeItem('selectedServices');
      localStorage.removeItem('selectedDate');
      localStorage.removeItem('selectedTime');
      localStorage.removeItem('totalPrice');

      toast({
        title: "預約成功",
        description: "您的預約已建立，我們將盡快為您安排",
      });

      navigate("/booking-success");
    } catch (error: any) {
      toast({
        title: "預約失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}小時${mins}分鐘`;
    } else if (hours > 0) {
      return `${hours}小時`;
    } else {
      return `${mins}分鐘`;
    }
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return format(date, "yyyy/MM/dd (EEEE)", { locale: zhCN });
  };

  if (!user || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <p className="text-muted-foreground">載入中...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 bg-background/95 backdrop-blur-sm border-b border-border z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-3">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate(-1)}
              className="rounded-full"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <p className="text-sm text-muted-foreground">返回修改</p>
            </div>
          </div>
          <h1 className="font-semibold">確認預約</h1>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Booking Summary */}
        <Card className="p-6 card-shadow">
          <h2 className="font-medium mb-4 flex items-center">
            <Calendar className="w-5 h-5 mr-2 text-primary" />
            預約資訊
          </h2>
          
          <div className="space-y-4">
            {/* Staff */}
            <div className="flex items-center space-x-3">
              <div className="text-2xl">👩‍💼</div>
              <div>
                <p className="font-medium text-foreground">專業美容師</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <MapPin className="w-3 h-3 mr-1" />
                  HOTCAKE 臺北市大安區
                </p>
              </div>
            </div>

            <Separator />

            {/* Services */}
            <div className="space-y-3">
              {services.map((service, index) => (
                <div key={service.id} className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="font-medium text-foreground">{service.name}</p>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <span className="flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDuration(service.duration_minutes)}
                      </span>
                    </div>
                    {service.description && (
                      <p className="text-sm text-muted-foreground mt-1">{service.description}</p>
                    )}
                  </div>
                  <p className="font-medium text-primary">${service.price_min}</p>
                </div>
              ))}
            </div>

            <Separator />

            {/* Date & Time */}
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">預約時間</span>
              <span className="font-medium text-foreground">
                {formatDate(selectedDate)} {selectedTime}
              </span>
            </div>
          </div>
        </Card>

        {/* Coupon Section */}
        <Card className="p-6 card-shadow">
          <h3 className="font-medium mb-4 flex items-center">
            <Ticket className="w-5 h-5 mr-2 text-primary" />
            使用票券
          </h3>
          
          {!appliedCoupon ? (
            <div className="flex space-x-2">
              <Input
                placeholder="請輸入票券代碼"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value)}
                className="flex-1"
              />
              <Button 
                variant="outline"
                onClick={handleApplyCoupon}
                disabled={!couponCode.trim()}
              >
                套用
              </Button>
            </div>
          ) : (
            <div className="bg-primary-lighter p-4 rounded-lg border border-primary/20">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-foreground">{appliedCoupon.name}</p>
                  <p className="text-sm text-muted-foreground">代碼: {appliedCoupon.code}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">折扣</p>
                  <p className="font-medium text-primary">
                    -{appliedCoupon.type === 'fixed' 
                      ? `$${appliedCoupon.value}` 
                      : `${appliedCoupon.value}%`}
                  </p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                className="mt-2"
                onClick={() => {
                  setAppliedCoupon(null);
                  setFinalPrice(totalPrice);
                  setCouponCode("");
                }}
              >
                移除票券
              </Button>
            </div>
          )}
        </Card>

        {/* Notes Section */}
        <Card className="p-6 card-shadow">
          <h3 className="font-medium mb-4">預約備註</h3>
          <Textarea
            placeholder="有什麼特殊需求或想跟服務人員說的話嗎？（選填）"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="min-h-20"
          />
        </Card>

        {/* Price Summary */}
        <Card className="p-6 card-shadow bg-primary-lighter border-primary/20">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">服務小計</span>
              <span className="font-medium">${totalPrice}</span>
            </div>
            
            {appliedCoupon && (
              <div className="flex justify-between items-center text-primary">
                <span>票券折扣</span>
                <span>-${totalPrice - finalPrice}</span>
              </div>
            )}
            
            <Separator />
            
            <div className="flex justify-between items-center text-lg font-semibold">
              <span>總金額</span>
              <span className="text-primary">${finalPrice}</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Bottom Action */}
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border p-4">
        <Button 
          className="w-full h-12 beauty-gradient text-white rounded-full font-medium smooth-transition"
          onClick={handleConfirmBooking}
          disabled={submitting}
        >
          {submitting ? "預約中..." : `確認預約並付款 $${finalPrice}`}
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default BookingConfirm;