import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Welcome = () => {
  const navigate = useNavigate();

  const handleLineLogin = () => {
    // 導向登入頁面
    navigate("/login");
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-soft to-background flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto space-y-8">
        {/* Logo Section */}
        <div className="text-center space-y-4">
          <div className="w-24 h-24 mx-auto bg-primary rounded-full flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full opacity-80"></div>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-foreground">Bella Beauty Safon</h1>
        </div>

        {/* Login <PERSON> */}
        <Card className="p-6 card-shadow">
          <Button 
            onClick={handleLineLogin}
            className="w-full h-14 beauty-gradient text-white font-semibold text-lg rounded-full smooth-transition hover:scale-105"
          >
            <MessageCircle className="w-6 h-6 mr-2" />
            使用 LINE 登入/預約
          </Button>
        </Card>

        {/* Service Categories Preview }
        <div className="space-y-3">
          <div className="flex items-center space-x-3 p-4 bg-card rounded-xl card-shadow smooth-transition hover:scale-102">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-warm to-primary-light rounded-lg flex-shrink-0"></div>
            <div>
              <h3 className="font-medium text-foreground">示範：美甲美睫美髮美容</h3>
              <p className="text-sm text-muted-foreground">臺北市大安區</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 p-4 bg-card rounded-xl card-shadow smooth-transition hover:scale-102">
            <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-primary-light rounded-lg flex-shrink-0"></div>
            <div>
              <h3 className="font-medium text-foreground">示範：按摩/整復</h3>
              <p className="text-sm text-muted-foreground">專業按摩服務</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 p-4 bg-card rounded-xl card-shadow smooth-transition hover:scale-102">
            <div className="w-12 h-12 bg-gradient-to-br from-accent to-primary-light rounded-lg flex-shrink-0"></div>
            <div>
              <h3 className="font-medium text-foreground">示範：寵物美容</h3>
              <p className="text-sm text-muted-foreground">寵物造型設計</p>
            </div>
          </div>
        </div>*/}

        {/* Footer */}
        <div className="text-center space-y-2">
          <p className="text-xs text-muted-foreground">Powered by Bella Beauty Safon</p>
          <p className="text-xs text-muted-foreground">v1.11.0</p>
        </div>
      </div>
    </div>
  );
};

export default Welcome;