import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Pencil, Trash2, Save, X, Calendar as CalendarIcon, Percent, Gift, Users } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";

interface Coupon {
  id: string;
  code: string;
  name: string;
  description: string;
  type: "percentage" | "fixed" | "free_service";
  value: number;
  minAmount: number;
  maxDiscount?: number;
  validFrom: Date;
  validTo: Date;
  usageLimit: number;
  usedCount: number;
  isActive: boolean;
  applicableServices: string[];
}

const AdminCouponManagement = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [shopId, setShopId] = useState<string>("");
  const [coupons, setCoupons] = useState<Coupon[]>([]);

  useEffect(() => {
    loadCoupons();
  }, []);

  const loadCoupons = async () => {
    try {
      // 使用測試店家 ID
      const currentShopId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
      setShopId(currentShopId);

      // 載入票券
      const { data: couponsData, error } = await supabase
        .from('coupons')
        .select('*')
        .eq('shop_id', currentShopId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading coupons:', error);
        toast({
          title: "載入失敗",
          description: `無法載入票券資料: ${error.message}`,
          variant: "destructive"
        });
        return;
      }

      if (couponsData) {
        setCoupons(couponsData.map(coupon => ({
          id: coupon.id,
          code: coupon.code,
          name: coupon.name,
          description: coupon.description || "",
          type: coupon.type as "percentage" | "fixed" | "free_service",
          value: coupon.value,
          minAmount: coupon.min_amount,
          maxDiscount: coupon.max_discount,
          validFrom: new Date(coupon.valid_from),
          validTo: new Date(coupon.valid_to),
          usageLimit: coupon.usage_limit,
          usedCount: coupon.used_count,
          isActive: coupon.is_active,
          applicableServices: coupon.applicable_services || ["all"]
        })));
      }
    } catch (error) {
      console.error('Error loading coupons:', error);
      toast({
        title: "載入失敗",
        description: `載入票券時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`,
        variant: "destructive"
      });
    }
  };

  const serviceCategories = ["all", "美髮造型", "美甲美睫", "臉部護理"];

  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    description: "",
    type: "percentage" as "percentage" | "fixed" | "free_service",
    value: 0,
    minAmount: 0,
    maxDiscount: undefined as number | undefined,
    validFrom: new Date(),
    validTo: new Date(),
    usageLimit: 1,
    applicableServices: ["all"] as string[]
  });
  const [isAdding, setIsAdding] = useState(false);

  const generateCouponCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData({...formData, code: result});
  };

  const handleAdd = () => {
    setIsAdding(true);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    
    setFormData({
      code: "",
      name: "",
      description: "",
      type: "percentage",
      value: 0,
      minAmount: 0,
      maxDiscount: undefined,
      validFrom: tomorrow,
      validTo: nextMonth,
      usageLimit: 1,
      applicableServices: ["all"]
    });
  };

  const handleEdit = (coupon: Coupon) => {
    setEditingId(coupon.id);
    setFormData({
      code: coupon.code,
      name: coupon.name,
      description: coupon.description,
      type: coupon.type,
      value: coupon.value,
      minAmount: coupon.minAmount,
      maxDiscount: coupon.maxDiscount,
      validFrom: coupon.validFrom,
      validTo: coupon.validTo,
      usageLimit: coupon.usageLimit,
      applicableServices: coupon.applicableServices
    });
  };

  const handleSave = async () => {
    if (!formData.code.trim() || !formData.name.trim() || !shopId) {
      toast({
        title: "錯誤",
        description: "請填寫票券代碼和名稱",
        variant: "destructive"
      });
      return;
    }

    if (formData.validFrom >= formData.validTo) {
      toast({
        title: "錯誤",
        description: "結束日期必須晚於開始日期",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      if (isAdding) {
        // 檢查代碼是否重複
        const { data: existing } = await supabase
          .from('coupons')
          .select('id')
          .eq('shop_id', shopId)
          .eq('code', formData.code)
          .maybeSingle();

        if (existing) {
          toast({
            title: "錯誤",
            description: "票券代碼已存在",
            variant: "destructive"
          });
          return;
        }

        const { data, error } = await supabase
          .from('coupons')
          .insert({
            shop_id: shopId,
            code: formData.code,
            name: formData.name,
            description: formData.description,
            type: formData.type,
            value: formData.value,
            min_amount: formData.minAmount,
            max_discount: formData.maxDiscount,
            valid_from: formData.validFrom.toISOString().split('T')[0],
            valid_to: formData.validTo.toISOString().split('T')[0],
            usage_limit: formData.usageLimit,
            applicable_services: formData.applicableServices
          })
          .select()
          .single();

        if (error) throw error;

        const newCoupon: Coupon = {
          id: data.id,
          code: data.code,
          name: data.name,
          description: data.description || "",
          type: data.type as "percentage" | "fixed" | "free_service",
          value: data.value,
          minAmount: data.min_amount,
          maxDiscount: data.max_discount,
          validFrom: new Date(data.valid_from),
          validTo: new Date(data.valid_to),
          usageLimit: data.usage_limit,
          usedCount: data.used_count,
          isActive: data.is_active,
          applicableServices: data.applicable_services || ["all"]
        };

        setCoupons([newCoupon, ...coupons]);
        setIsAdding(false);
        toast({
          title: "成功",
          description: "票券已新增"
        });
      } else if (editingId) {
        const { error } = await supabase
          .from('coupons')
          .update({
            code: formData.code,
            name: formData.name,
            description: formData.description,
            type: formData.type,
            value: formData.value,
            min_amount: formData.minAmount,
            max_discount: formData.maxDiscount,
            valid_from: formData.validFrom.toISOString().split('T')[0],
            valid_to: formData.validTo.toISOString().split('T')[0],
            usage_limit: formData.usageLimit,
            applicable_services: formData.applicableServices
          })
          .eq('id', editingId);

        if (error) throw error;

        setCoupons(coupons.map(coupon => 
          coupon.id === editingId 
            ? { ...coupon, ...formData }
            : coupon
        ));
        setEditingId(null);
        toast({
          title: "成功", 
          description: "票券已更新"
        });
      }

      setFormData({
        code: "",
        name: "",
        description: "",
        type: "percentage",
        value: 0,
        minAmount: 0,
        maxDiscount: undefined,
        validFrom: new Date(),
        validTo: new Date(),
        usageLimit: 1,
        applicableServices: ["all"]
      });
    } catch (error) {
      console.error('Error saving coupon:', error);
      toast({
        title: "儲存失敗",
        description: "無法儲存票券",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from('coupons')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setCoupons(coupons.filter(coupon => coupon.id !== id));
      toast({
        title: "成功",
        description: "票券已刪除"
      });
    } catch (error) {
      console.error('Error deleting coupon:', error);
      toast({
        title: "刪除失敗",
        description: "無法刪除票券",
        variant: "destructive"
      });
    }
  };

  const toggleStatus = async (id: string) => {
    try {
      const coupon = coupons.find(c => c.id === id);
      if (!coupon) return;

      const { error } = await supabase
        .from('coupons')
        .update({ is_active: !coupon.isActive })
        .eq('id', id);

      if (error) throw error;

      setCoupons(coupons.map(coupon => 
        coupon.id === id ? { ...coupon, isActive: !coupon.isActive } : coupon
      ));
    } catch (error) {
      console.error('Error toggling coupon status:', error);
      toast({
        title: "更新失敗",
        description: "無法更新票券狀態",
        variant: "destructive"
      });
    }
  };

  const getCouponTypeText = (type: string) => {
    switch (type) {
      case "percentage": return "百分比折扣";
      case "fixed": return "固定金額";
      case "free_service": return "免費服務";
      default: return type;
    }
  };

  const getCouponValueText = (coupon: Coupon) => {
    switch (coupon.type) {
      case "percentage":
        return `${coupon.value}% 折扣${coupon.maxDiscount ? ` (最高折 $${coupon.maxDiscount})` : ""}`;
      case "fixed":
        return `折抵 $${coupon.value}`;
      case "free_service":
        return "免費服務";
      default:
        return `$${coupon.value}`;
    }
  };

  const CouponForm = () => (
    <div className="border rounded-lg p-4 bg-gray-50">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="code">票券代碼 *</Label>
          <div className="flex gap-2">
            <Input
              id="code"
              value={formData.code}
              onChange={(e) => setFormData({...formData, code: e.target.value.toUpperCase()})}
              placeholder="請輸入票券代碼"
            />
            <Button type="button" variant="outline" onClick={generateCouponCode}>
              產生
            </Button>
          </div>
        </div>
        <div>
          <Label htmlFor="name">票券名稱 *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            placeholder="請輸入票券名稱"
          />
        </div>
        <div>
          <Label htmlFor="type">優惠類型</Label>
          <Select value={formData.type} onValueChange={(value: any) => setFormData({...formData, type: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="percentage">百分比折扣</SelectItem>
              <SelectItem value="fixed">固定金額折扣</SelectItem>
              <SelectItem value="free_service">免費服務</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="value">
            {formData.type === "percentage" ? "折扣百分比" : "折扣金額"}
          </Label>
          <Input
            id="value"
            type="number"
            value={formData.value}
            onChange={(e) => setFormData({...formData, value: parseInt(e.target.value) || 0})}
            placeholder={formData.type === "percentage" ? "例如：20 (代表8折)" : "例如：500"}
            min="0"
            max={formData.type === "percentage" ? "100" : undefined}
          />
        </div>
        <div>
          <Label htmlFor="minAmount">最低消費金額</Label>
          <Input
            id="minAmount"
            type="number"
            value={formData.minAmount}
            onChange={(e) => setFormData({...formData, minAmount: parseInt(e.target.value) || 0})}
            min="0"
          />
        </div>
        {formData.type === "percentage" && (
          <div>
            <Label htmlFor="maxDiscount">最高折扣金額</Label>
            <Input
              id="maxDiscount"
              type="number"
              value={formData.maxDiscount || ""}
              onChange={(e) => setFormData({...formData, maxDiscount: parseInt(e.target.value) || undefined})}
              placeholder="不限制請留空"
              min="0"
            />
          </div>
        )}
        <div>
          <Label>有效期間開始</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal", !formData.validFrom && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.validFrom ? format(formData.validFrom, "yyyy/MM/dd") : "選擇日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.validFrom}
                onSelect={(date) => date && setFormData({...formData, validFrom: date})}
                initialFocus
                className="pointer-events-auto"
              />
            </PopoverContent>
          </Popover>
        </div>
        <div>
          <Label>有效期間結束</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal", !formData.validTo && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.validTo ? format(formData.validTo, "yyyy/MM/dd") : "選擇日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.validTo}
                onSelect={(date) => date && setFormData({...formData, validTo: date})}
                initialFocus
                className="pointer-events-auto"
              />
            </PopoverContent>
          </Popover>
        </div>
        <div>
          <Label htmlFor="usageLimit">使用次數限制</Label>
          <Input
            id="usageLimit"
            type="number"
            value={formData.usageLimit}
            onChange={(e) => setFormData({...formData, usageLimit: parseInt(e.target.value) || 1})}
            min="1"
          />
        </div>
        <div>
          <Label>適用服務</Label>
          <Select 
            value={formData.applicableServices[0]} 
            onValueChange={(value) => setFormData({...formData, applicableServices: [value]})}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {serviceCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category === "all" ? "所有服務" : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="md:col-span-2">
          <Label htmlFor="description">票券描述</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            placeholder="請輸入票券使用說明"
            rows={3}
          />
        </div>
      </div>
      <div className="flex gap-2 mt-4">
        <Button 
          onClick={handleSave} 
          className="flex items-center gap-2"
          disabled={loading}
        >
          <Save className="h-4 w-4" />
          {loading ? "儲存中..." : "儲存"}
        </Button>
        <Button onClick={handleCancel} variant="outline" className="flex items-center gap-2">
          <X className="h-4 w-4" />
          取消
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">票券管理</h1>
            <p className="text-gray-600">管理優惠票券與折扣代碼</p>
          </div>
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>票券列表</CardTitle>
              <Button onClick={handleAdd} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                新增票券
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isAdding && <CouponForm />}

                {coupons.map((coupon) => (
                  <div key={coupon.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    {editingId === coupon.id ? (
                      <CouponForm />
                    ) : (
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge variant="secondary" className="font-mono text-lg px-3 py-1">
                              {coupon.code}
                            </Badge>
                            <h3 className="font-semibold text-lg">{coupon.name}</h3>
                            <Badge className={coupon.type === "percentage" ? "bg-green-100 text-green-800" : 
                                           coupon.type === "fixed" ? "bg-blue-100 text-blue-800" : 
                                           "bg-purple-100 text-purple-800"}>
                              {getCouponTypeText(coupon.type)}
                            </Badge>
                          </div>
                          <p className="text-gray-600 mb-3">{coupon.description}</p>
                          <div className="flex items-center gap-6 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Gift className="h-4 w-4" />
                              <span>{getCouponValueText(coupon)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <CalendarIcon className="h-4 w-4" />
                              <span>{format(coupon.validFrom, "yyyy/MM/dd")} - {format(coupon.validTo, "yyyy/MM/dd")}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              <span>{coupon.usedCount} / {coupon.usageLimit} 已使用</span>
                            </div>
                            <div>
                              <span>適用: {coupon.applicableServices[0] === "all" ? "所有服務" : coupon.applicableServices.join(", ")}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant={coupon.isActive ? "default" : "secondary"}
                            size="sm"
                            onClick={() => toggleStatus(coupon.id)}
                          >
                            {coupon.isActive ? "啟用中" : "已停用"}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(coupon)}
                            className="flex items-center gap-2"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(coupon.id)}
                            className="flex items-center gap-2 text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminCouponManagement;