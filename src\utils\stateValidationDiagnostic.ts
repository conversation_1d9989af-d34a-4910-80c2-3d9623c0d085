/**
 * State 驗證診斷工具
 * 專門用於診斷「安全驗證失敗」錯誤
 */

export interface StateValidationResult {
  step: string;
  success: boolean;
  message: string;
  details?: any;
  recommendation?: string;
}

/**
 * 診斷 state 驗證問題
 */
export const diagnoseStateValidation = (urlParams: URLSearchParams): StateValidationResult[] => {
  const results: StateValidationResult[] = [];
  
  // 1. 檢查 URL 中的 state 參數
  const urlState = urlParams.get("state");
  results.push({
    step: "URL State 參數檢查",
    success: !!urlState,
    message: urlState ? `URL 中有 state 參數: ${urlState.substring(0, 8)}...` : "URL 中沒有 state 參數",
    details: { urlState },
    recommendation: !urlState ? "檢查 LINE Login URL 是否正確包含 state 參數" : undefined
  });

  // 2. 檢查 localStorage 中的 state
  const savedState = localStorage.getItem("line_login_state");
  results.push({
    step: "LocalStorage State 檢查",
    success: !!savedState,
    message: savedState ? `LocalStorage 中有 state: ${savedState.substring(0, 8)}...` : "LocalStorage 中沒有 state",
    details: { savedState },
    recommendation: !savedState ? "可能是瀏覽器清除了 localStorage 或隱私模式限制" : undefined
  });

  // 3. 比較兩個 state 是否一致
  if (urlState && savedState) {
    const statesMatch = urlState === savedState;
    results.push({
      step: "State 一致性檢查",
      success: statesMatch,
      message: statesMatch ? "URL 和 LocalStorage 中的 state 一致" : "State 參數不一致",
      details: { 
        urlState: urlState.substring(0, 8) + "...",
        savedState: savedState.substring(0, 8) + "...",
        match: statesMatch 
      },
      recommendation: !statesMatch ? "可能是多個登入流程同時進行或頁面被重新整理" : undefined
    });
  }

  // 4. 檢查 localStorage 可用性
  try {
    const testKey = 'test_' + Date.now();
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    results.push({
      step: "LocalStorage 可用性檢查",
      success: true,
      message: "LocalStorage 功能正常",
      details: { localStorageAvailable: true }
    });
  } catch (error) {
    results.push({
      step: "LocalStorage 可用性檢查",
      success: false,
      message: "LocalStorage 不可用",
      details: { error, localStorageAvailable: false },
      recommendation: "檢查瀏覽器隱私設定或嘗試使用無痕模式"
    });
  }

  // 5. 檢查是否有其他 LINE 相關的 localStorage 項目
  const lineRelatedKeys = Object.keys(localStorage).filter(key => 
    key.includes('line') || key.includes('auth') || key.includes('state')
  );
  results.push({
    step: "相關 Storage 項目檢查",
    success: true,
    message: `找到 ${lineRelatedKeys.length} 個相關的 storage 項目`,
    details: { lineRelatedKeys },
    recommendation: lineRelatedKeys.length > 5 ? "考慮清除過多的 storage 項目" : undefined
  });

  return results;
};

/**
 * 修復 state 驗證問題
 */
export const fixStateValidationIssues = (): void => {
  // 清除所有 LINE 相關的 localStorage 項目
  const keysToRemove = Object.keys(localStorage).filter(key => 
    key.includes('line') || key.includes('auth') || key.includes('state')
  );
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
  });

  console.log(`已清除 ${keysToRemove.length} 個相關的 storage 項目:`, keysToRemove);
};

/**
 * 生成新的 state 並儲存
 */
export const generateNewState = (): string => {
  const newState = Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(36).substring(2, 15);
  
  try {
    localStorage.setItem('line_login_state', newState);
    console.log('已生成新的 state:', newState.substring(0, 8) + '...');
    return newState;
  } catch (error) {
    console.error('無法儲存 state 到 localStorage:', error);
    throw new Error('LocalStorage 不可用，請檢查瀏覽器設定');
  }
};

/**
 * 檢查當前頁面是否為 callback 頁面
 */
export const isCallbackPage = (): boolean => {
  return window.location.pathname.includes('/auth/line/callback');
};

/**
 * 獲取 callback URL 參數
 */
export const getCallbackParams = (): URLSearchParams => {
  return new URLSearchParams(window.location.search);
};

/**
 * 完整的 state 驗證診斷
 */
export const runStateValidationDiagnostic = (): {
  results: StateValidationResult[];
  canProceed: boolean;
  recommendations: string[];
} => {
  const params = getCallbackParams();
  const results = diagnoseStateValidation(params);
  
  // 判斷是否可以繼續
  const criticalFailures = results.filter(r => 
    !r.success && (
      r.step.includes('State 一致性檢查') || 
      r.step.includes('LocalStorage 可用性檢查')
    )
  );
  
  const canProceed = criticalFailures.length === 0;
  
  // 生成建議
  const recommendations: string[] = [];
  results.forEach(result => {
    if (result.recommendation) {
      recommendations.push(result.recommendation);
    }
  });
  
  if (!canProceed) {
    recommendations.push("建議清除瀏覽器 localStorage 並重新開始登入流程");
    recommendations.push("如果問題持續，請嘗試使用無痕模式");
  }
  
  return {
    results,
    canProceed,
    recommendations
  };
};

/**
 * 在控制台輸出 state 診斷報告
 */
export const logStateValidationReport = (): void => {
  console.group("🔒 State 驗證診斷報告");
  
  const diagnostic = runStateValidationDiagnostic();
  
  console.log("=== 診斷結果 ===");
  diagnostic.results.forEach((result, index) => {
    const icon = result.success ? "✅" : "❌";
    console.log(`${index + 1}. ${icon} ${result.step}: ${result.message}`);
    if (result.details) {
      console.log("   詳細:", result.details);
    }
    if (result.recommendation) {
      console.log("   建議:", result.recommendation);
    }
  });
  
  console.log("\n=== 總結 ===");
  console.log(`可以繼續: ${diagnostic.canProceed ? "是" : "否"}`);
  
  if (diagnostic.recommendations.length > 0) {
    console.log("\n=== 修復建議 ===");
    diagnostic.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
  
  console.groupEnd();
};

/**
 * 自動修復常見的 state 驗證問題
 */
export const autoFixStateIssues = (): boolean => {
  try {
    console.log("🔧 開始自動修復 state 驗證問題...");

    // 1. 清除舊的 state 資料
    fixStateValidationIssues();

    // 2. 檢查 localStorage 是否可用
    const testKey = 'test_' + Date.now();
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);

    console.log("✅ 自動修復完成");
    return true;
  } catch (error) {
    console.error("❌ 自動修復失敗:", error);
    return false;
  }
};

/**
 * 緊急 state 恢復機制
 * 當 localStorage 中沒有 state 但 URL 中有時使用
 */
export const emergencyStateRecovery = (urlState: string): boolean => {
  try {
    console.log("🚨 啟動緊急 state 恢復機制...");

    // 檢查 URL state 的有效性
    if (!urlState || urlState.length < 8) {
      console.error("❌ URL state 無效");
      return false;
    }

    // 檢查 state 是否是最近生成的（基於時間戳）
    const now = Date.now();
    const stateTimestamp = parseInt(urlState.slice(-8), 36) * 1000; // 假設 state 包含時間戳
    const timeDiff = now - stateTimestamp;

    // 如果 state 是在 10 分鐘內生成的，認為是有效的
    if (timeDiff < 10 * 60 * 1000) {
      console.log("✅ State 時間戳驗證通過，允許緊急恢復");
      localStorage.setItem('line_login_state', urlState);
      return true;
    }

    console.error("❌ State 已過期，拒絕恢復");
    return false;
  } catch (error) {
    console.error("❌ 緊急恢復失敗:", error);
    return false;
  }
};

/**
 * 增強的 state 生成（包含時間戳）
 */
export const generateEnhancedState = (): string => {
  const randomPart = Math.random().toString(36).substring(2, 10);
  const timestamp = Math.floor(Date.now() / 1000).toString(36);
  return `${randomPart}${timestamp}`;
};

/**
 * 驗證 state 的時間有效性
 */
export const validateStateTimestamp = (state: string): boolean => {
  try {
    if (state.length < 8) return false;

    const timestampPart = state.slice(-8);
    const timestamp = parseInt(timestampPart, 36) * 1000;
    const now = Date.now();
    const timeDiff = now - timestamp;

    // 10 分鐘有效期
    return timeDiff < 10 * 60 * 1000 && timeDiff > 0;
  } catch {
    return false;
  }
};
