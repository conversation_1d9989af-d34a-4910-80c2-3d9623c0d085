import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Ticket, Clock, Gift, Calendar } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const CouponManagement = () => {
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState("available");

  const coupons = {
    available: [
      {
        id: "WELCOME200",
        title: "新會員專屬優惠",
        description: "首次消費折抵 $200",
        discount: 200,
        type: "amount",
        expiry: "2024/12/31",
        minSpend: 1000,
        isUsed: false
      },
      {
        id: "BIRTHDAY50",
        title: "生日快樂優惠券",
        description: "生日當月享 5 折優惠",
        discount: 50,
        type: "percentage",
        expiry: "2024/08/31",
        minSpend: 0,
        isUsed: false
      }
    ],
    used: [
      {
        id: "SUMMER100",
        title: "夏日清爽優惠",
        description: "臉部保養服務折抵 $100",
        discount: 100,
        type: "amount",
        expiry: "2024/07/31",
        minSpend: 500,
        isUsed: true,
        usedDate: "2024/07/15"
      }
    ],
    expired: []
  };

  const getDiscountText = (coupon: any) => {
    if (coupon.type === 'percentage') {
      return `${coupon.discount}% 折扣`;
    }
    return `$${coupon.discount} 折抵`;
  };

  const isExpiringSoon = (expiry: string) => {
    const expiryDate = new Date(expiry);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays > 0;
  };

  const tabs = [
    { id: 'available', label: '可使用', count: coupons.available.length },
    { id: 'used', label: '已使用', count: coupons.used.length },
    { id: 'expired', label: '已過期', count: coupons.expired.length }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">票券管理</h1>
          <div className="w-10"></div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{coupons.available.length}</div>
            <div className="text-xs text-muted-foreground">可使用</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{coupons.used.length}</div>
            <div className="text-xs text-muted-foreground">已使用</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{coupons.expired.length}</div>
            <div className="text-xs text-muted-foreground">已過期</div>
          </Card>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-border">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      <div className="p-4 space-y-4">
        {coupons[activeTab as keyof typeof coupons].length > 0 ? (
          coupons[activeTab as keyof typeof coupons].map((coupon) => (
            <Card key={coupon.id} className={`p-4 card-shadow ${coupon.isUsed ? 'opacity-60' : ''}`}>
              <div className="space-y-4">
                {/* Coupon Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className={`w-12 h-12 ${coupon.isUsed ? 'bg-gray-50' : 'bg-primary-lighter'} rounded-lg flex items-center justify-center`}>
                      <Ticket className={`w-6 h-6 ${coupon.isUsed ? 'text-gray-400' : 'text-primary'}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-foreground">{coupon.title}</h3>
                      <p className="text-sm text-muted-foreground">{coupon.description}</p>
                    </div>
                  </div>
                  {!coupon.isUsed && isExpiringSoon(coupon.expiry) && (
                    <Badge variant="destructive" className="text-xs">
                      即將過期
                    </Badge>
                  )}
                  {coupon.isUsed && (
                    <Badge variant="secondary" className="text-xs">
                      已使用
                    </Badge>
                  )}
                </div>

                {/* Discount Info */}
                <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-3 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-primary">
                      {getDiscountText(coupon)}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      代碼: {coupon.id}
                    </span>
                  </div>
                </div>

                {/* Coupon Details */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-muted-foreground">有效期限</span>
                    </div>
                    <span className="text-foreground">{coupon.expiry}</span>
                  </div>
                  
                  {coupon.minSpend > 0 && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">最低消費</span>
                      <span className="text-foreground">${coupon.minSpend}</span>
                    </div>
                  )}
                  
                  {coupon.isUsed && coupon.usedDate && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">使用日期</span>
                      <span className="text-foreground">{coupon.usedDate}</span>
                    </div>
                  )}
                </div>

                {/* Action Button */}
                {!coupon.isUsed && (
                  <Button 
                    className="w-full"
                    onClick={() => navigate("/services")}
                  >
                    <Gift className="w-4 h-4 mr-2" />
                    立即使用
                  </Button>
                )}
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <Ticket className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium text-foreground mb-2">
              沒有{tabs.find(t => t.id === activeTab)?.label}的票券
            </h3>
            <p className="text-muted-foreground mb-4">
              {activeTab === 'available' && '您目前沒有可使用的票券'}
              {activeTab === 'used' && '您還沒有使用過任何票券'}
              {activeTab === 'expired' && '您沒有過期的票券'}
            </p>
            {activeTab === 'available' && (
              <Button onClick={() => navigate("/rewards")}>
                <Gift className="w-4 h-4 mr-2" />
                獲取更多票券
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CouponManagement;