import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ArrowLeft, Camera, Bell, Shield, Moon, Globe } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const PersonalSettings = () => {
  const navigate = useNavigate();
  
  const [settings, setSettings] = useState({
    name: "王小明",
    phone: "0912-345-678",
    email: "<EMAIL>",
    notifications: true,
    darkMode: false,
    language: "zh-TW"
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">個人設定</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Profile Section */}
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <div className="relative">
                <Avatar className="w-20 h-20">
                  <AvatarFallback className="text-2xl font-semibold">
                    {settings.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <Button 
                  size="icon" 
                  className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full"
                >
                  <Camera className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">姓名</Label>
                <Input 
                  id="name"
                  value={settings.name}
                  onChange={(e) => handleSettingChange('name', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">電話號碼</Label>
                <Input 
                  id="phone"
                  value={settings.phone}
                  onChange={(e) => handleSettingChange('phone', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">電子郵件</Label>
                <Input 
                  id="email"
                  type="email"
                  value={settings.email}
                  onChange={(e) => handleSettingChange('email', e.target.value)}
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Notification Settings */}
        <Card className="p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Bell className="w-5 h-5 text-primary" />
              <h3 className="font-semibold text-foreground">通知設定</h3>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-foreground">推播通知</p>
                <p className="text-sm text-muted-foreground">接收預約提醒和優惠資訊</p>
              </div>
              <Switch 
                checked={settings.notifications}
                onCheckedChange={(checked) => handleSettingChange('notifications', checked)}
              />
            </div>
          </div>
        </Card>

        {/* App Settings */}
        <Card className="p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Globe className="w-5 h-5 text-primary" />
              <h3 className="font-semibold text-foreground">應用程式設定</h3>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-foreground">深色模式</p>
                <p className="text-sm text-muted-foreground">切換應用程式外觀</p>
              </div>
              <Switch 
                checked={settings.darkMode}
                onCheckedChange={(checked) => handleSettingChange('darkMode', checked)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>語言設定</Label>
              <select 
                className="w-full p-2 border border-input rounded-md bg-background text-foreground"
                value={settings.language}
                onChange={(e) => handleSettingChange('language', e.target.value)}
              >
                <option value="zh-TW">繁體中文</option>
                <option value="zh-CN">简体中文</option>
                <option value="en">English</option>
              </select>
            </div>
          </div>
        </Card>

        {/* Privacy & Security */}
        <Card className="p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Shield className="w-5 h-5 text-primary" />
              <h3 className="font-semibold text-foreground">隱私與安全</h3>
            </div>
            
            <Button variant="outline" className="w-full justify-start">
              修改密碼
            </Button>
            
            <Button variant="outline" className="w-full justify-start">
              隱私政策
            </Button>
            
            <Button variant="outline" className="w-full justify-start text-red-600 border-red-200">
              刪除帳號
            </Button>
          </div>
        </Card>

        {/* Save Button */}
        <Button className="w-full h-12 beauty-gradient text-white">
          儲存設定
        </Button>
      </div>
    </div>
  );
};

export default PersonalSettings;