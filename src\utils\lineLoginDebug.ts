/**
 * LINE Login 除錯工具
 * 幫助診斷和修復 LINE Login 相關問題
 */

export interface LineLoginDebugInfo {
  currentOrigin: string;
  expectedRedirectUri: string;
  currentRedirectUri?: string;
  channelId?: string;
  issues: string[];
  suggestions: string[];
}

/**
 * 檢查 LINE Login 設定並提供除錯資訊
 */
export const debugLineLogin = async (): Promise<LineLoginDebugInfo> => {
  const currentOrigin = window.location.origin;
  const expectedRedirectUri = `${currentOrigin}/auth/line/callback`;
  const issues: string[] = [];
  const suggestions: string[] = [];

  // 檢查當前環境
  if (currentOrigin.includes('localhost')) {
    suggestions.push('開發環境：請確保在 LINE Developers Console 中註冊了 localhost callback URL');
  } else if (currentOrigin.includes('lovable.app')) {
    suggestions.push('Lovable 生產環境：請確保在 LINE Developers Console 中註冊了 https://line-beauty-appoint.lovable.app/auth/line/callback');
  } else {
    suggestions.push('生產環境：請確保使用 HTTPS 並在 LINE Developers Console 中註冊了正確的域名');
  }

  // 檢查端口
  const port = window.location.port;
  if (port && port !== '80' && port !== '443') {
    suggestions.push(`注意：您的應用運行在端口 ${port}，請確保 callback URL 包含此端口號`);
  }

  // 基本檢查
  if (!currentOrigin.startsWith('http')) {
    issues.push('無效的 origin，請確保使用正確的協議 (http/https)');
  }

  return {
    currentOrigin,
    expectedRedirectUri,
    issues,
    suggestions
  };
};

/**
 * 生成 LINE Login URL 用於測試
 */
export const generateTestLineLoginUrl = (channelId: string, redirectUri?: string): string => {
  if (!channelId) {
    throw new Error('Channel ID 是必需的');
  }

  const finalRedirectUri = redirectUri || `${window.location.origin}/auth/line/callback`;
  const state = Math.random().toString(36).substring(2, 15);
  
  // 儲存 state 用於驗證
  localStorage.setItem('line_login_state', state);

  const lineLoginUrl = new URL('https://access.line.me/oauth2/v2.1/authorize');
  lineLoginUrl.searchParams.append('response_type', 'code');
  lineLoginUrl.searchParams.append('client_id', channelId);
  lineLoginUrl.searchParams.append('redirect_uri', finalRedirectUri);
  lineLoginUrl.searchParams.append('state', state);
  lineLoginUrl.searchParams.append('scope', 'profile openid email');

  return lineLoginUrl.toString();
};

/**
 * 驗證 redirect_uri 格式
 */
export const validateRedirectUri = (uri: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!uri) {
    errors.push('Redirect URI 不能為空');
    return { valid: false, errors };
  }

  try {
    const url = new URL(uri);
    
    // 檢查協議
    if (!['http:', 'https:'].includes(url.protocol)) {
      errors.push('Redirect URI 必須使用 http 或 https 協議');
    }

    // 檢查路徑
    if (!url.pathname.includes('/auth/line/callback')) {
      errors.push('Redirect URI 路徑應該包含 /auth/line/callback');
    }

    // 生產環境建議使用 HTTPS
    if (url.protocol === 'http:' && !url.hostname.includes('localhost')) {
      errors.push('生產環境建議使用 HTTPS');
    }

  } catch (error) {
    errors.push('Redirect URI 格式無效');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 在控制台輸出除錯資訊
 */
export const logDebugInfo = async (): Promise<void> => {
  const debugInfo = await debugLineLogin();
  
  console.group('🔍 LINE Login 除錯資訊');
  console.log('當前 Origin:', debugInfo.currentOrigin);
  console.log('建議的 Redirect URI:', debugInfo.expectedRedirectUri);
  
  if (debugInfo.issues.length > 0) {
    console.group('❌ 發現問題:');
    debugInfo.issues.forEach(issue => console.log('•', issue));
    console.groupEnd();
  }
  
  if (debugInfo.suggestions.length > 0) {
    console.group('💡 建議:');
    debugInfo.suggestions.forEach(suggestion => console.log('•', suggestion));
    console.groupEnd();
  }
  
  console.groupEnd();
};
