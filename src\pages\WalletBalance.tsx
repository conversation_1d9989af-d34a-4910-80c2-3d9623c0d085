import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Plus, CreditCard, History } from "lucide-react";
import { useNavigate } from "react-router-dom";

const WalletBalance = () => {
  const navigate = useNavigate();

  const balance = 0;
  const transactions = [
    { id: 1, type: "charge", amount: 1000, date: "2024/07/10", description: "儲值" },
    { id: 2, type: "consume", amount: -800, date: "2024/07/12", description: "美甲服務" },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">目前儲值金</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Balance Card */}
        <Card className="p-6 text-center beauty-gradient text-white">
          <div className="space-y-2">
            <p className="text-sm opacity-90">可用餘額</p>
            <p className="text-4xl font-bold">${balance}</p>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-4">
          <Button className="h-12 space-x-2">
            <Plus className="w-4 h-4" />
            <span>儲值</span>
          </Button>
          <Button variant="outline" className="h-12 space-x-2">
            <CreditCard className="w-4 h-4" />
            <span>付款記錄</span>
          </Button>
        </div>

        {/* Transaction History */}
        <div className="space-y-3">
          <h3 className="font-semibold text-foreground flex items-center">
            <History className="w-4 h-4 mr-2" />
            交易記錄
          </h3>
          
          {transactions.length > 0 ? (
            transactions.map((transaction) => (
              <Card key={transaction.id} className="p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-foreground">{transaction.description}</p>
                    <p className="text-sm text-muted-foreground">{transaction.date}</p>
                  </div>
                  <div className={`font-semibold ${
                    transaction.type === 'charge' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.type === 'charge' ? '+' : ''}${Math.abs(transaction.amount)}
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>目前沒有交易記錄</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WalletBalance;