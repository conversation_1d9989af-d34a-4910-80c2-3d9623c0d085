-- Add test admin users and initial shop data
-- First, let's add a test shop
INSERT INTO public.shops (id, name, description, location, is_active) 
VALUES (
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '測試店家',
  '這是一個測試店家',
  '台北市信義區',
  true
) ON CONFLICT (id) DO NOTHING;

-- Create a test admin user (using a fixed UUID for testing)
-- In production, this would be the actual user's UUID from auth.users
INSERT INTO public.admin_users (user_id, shop_id, role, is_active)
VALUES (
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
  'admin',
  true
) ON CONFLICT (user_id, shop_id) DO NOTHING;

-- Add another test admin for different scenarios
INSERT INTO public.admin_users (user_id, shop_id, role, is_active)
VALUES (
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'admin', 
  true
) ON CONFLICT (user_id, shop_id) DO NOTHING;

-- Add initial LINE settings for the test shop
INSERT INTO public.line_settings (shop_id, channel_id, channel_secret, redirect_uri)
VALUES (
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '',
  '',
  'https://your-domain.com/auth/callback'
) ON CONFLICT (shop_id) DO NOTHING;

-- Temporarily modify RLS policies to allow operations for testing
-- Create a function that returns true for testing purposes
CREATE OR REPLACE FUNCTION public.is_test_admin()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT true; -- Temporarily allow all operations for testing
$$;

-- Add temporary policies that use the test function
CREATE POLICY IF NOT EXISTS "臨時測試政策 - LINE設定查看" 
ON public.line_settings 
FOR SELECT 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - LINE設定插入" 
ON public.line_settings 
FOR INSERT 
WITH CHECK (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - LINE設定更新" 
ON public.line_settings 
FOR UPDATE 
USING (public.is_test_admin());

-- Add similar temporary policies for other tables
CREATE POLICY IF NOT EXISTS "臨時測試政策 - 營業時間查看" 
ON public.business_hours 
FOR SELECT 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 營業時間插入" 
ON public.business_hours 
FOR INSERT 
WITH CHECK (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 營業時間更新" 
ON public.business_hours 
FOR UPDATE 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 時段查看" 
ON public.time_slots 
FOR SELECT 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 時段插入" 
ON public.time_slots 
FOR INSERT 
WITH CHECK (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 時段更新" 
ON public.time_slots 
FOR UPDATE 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 時段刪除" 
ON public.time_slots 
FOR DELETE 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 票券查看" 
ON public.coupons 
FOR SELECT 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 票券插入" 
ON public.coupons 
FOR INSERT 
WITH CHECK (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 票券更新" 
ON public.coupons 
FOR UPDATE 
USING (public.is_test_admin());

CREATE POLICY IF NOT EXISTS "臨時測試政策 - 票券刪除" 
ON public.coupons 
FOR DELETE 
USING (public.is_test_admin());