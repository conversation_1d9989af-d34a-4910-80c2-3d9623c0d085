import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Pencil, Trash2, Save, X } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface Category {
  id: string;
  name: string;
  slug: string;
  sort_order: number;
  is_active: boolean;
  service_count?: number;
}

const CategoryManagement = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    slug: ""
  });
  const [isAdding, setIsAdding] = useState(false);
  const [loading, setLoading] = useState(true);

  const SHOP_ID = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('service_categories')
        .select('*')
        .eq('shop_id', SHOP_ID)
        .order('sort_order');

      if (categoriesError) throw categoriesError;

      // 為每個分類計算服務數量
      const categoriesWithCounts = await Promise.all(
        (categoriesData || []).map(async (category) => {
          const { count } = await supabase
            .from('services')
            .select('*', { count: 'exact' })
            .eq('category_id', category.id);

          return {
            ...category,
            service_count: count || 0
          };
        })
      );

      setCategories(categoriesWithCounts);
    } catch (error: any) {
      toast({
        title: "載入分類失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fff]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  };

  const handleAdd = () => {
    setIsAdding(true);
    setFormData({ name: "", slug: "" });
  };

  const handleEdit = (category: Category) => {
    setEditingId(category.id);
    setFormData({
      name: category.name,
      slug: category.slug
    });
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "錯誤",
        description: "請輸入分類名稱",
        variant: "destructive"
      });
      return;
    }

    const slug = formData.slug.trim() || generateSlug(formData.name);

    try {
      if (isAdding) {
        // 獲取下一個排序號
        const { data: maxSortData } = await supabase
          .from('service_categories')
          .select('sort_order')
          .eq('shop_id', SHOP_ID)
          .order('sort_order', { ascending: false })
          .limit(1);

        const nextSortOrder = (maxSortData?.[0]?.sort_order || 0) + 1;

        const { error } = await supabase
          .from('service_categories')
          .insert({
            shop_id: SHOP_ID,
            name: formData.name,
            slug: slug,
            sort_order: nextSortOrder,
            is_active: true
          });

        if (error) throw error;

        toast({
          title: "成功",
          description: "分類已新增"
        });
        setIsAdding(false);
      } else if (editingId) {
        const { error } = await supabase
          .from('service_categories')
          .update({
            name: formData.name,
            slug: slug
          })
          .eq('id', editingId);

        if (error) throw error;

        toast({
          title: "成功", 
          description: "分類已更新"
        });
        setEditingId(null);
      }

      setFormData({ name: "", slug: "" });
      await fetchCategories();
    } catch (error: any) {
      toast({
        title: "操作失敗",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setFormData({ name: "", slug: "" });
  };

  const handleDelete = async (id: string) => {
    const category = categories.find(cat => cat.id === id);
    if (category && (category.service_count || 0) > 0) {
      toast({
        title: "無法刪除",
        description: "此分類下還有服務項目，請先移除所有服務項目",
        variant: "destructive"
      });
      return;
    }

    if (!confirm('確定要刪除此分類嗎？此操作無法復原。')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('service_categories')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "成功",
        description: "分類已刪除"
      });

      await fetchCategories();
    } catch (error: any) {
      toast({
        title: "刪除失敗",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const toggleStatus = async (id: string) => {
    const category = categories.find(cat => cat.id === id);
    if (!category) return;

    try {
      const { error } = await supabase
        .from('service_categories')
        .update({ is_active: !category.is_active })
        .eq('id', id);

      if (error) throw error;

      await fetchCategories();
    } catch (error: any) {
      toast({
        title: "更新狀態失敗",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">服務分類管理</h1>
            <p className="text-gray-600">管理服務項目的分類設定</p>
          </div>
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>分類列表</CardTitle>
              <Button onClick={handleAdd} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                新增分類
              </Button>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">載入中...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {isAdding && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="name">分類名稱</Label>
                          <Input
                            id="name"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            placeholder="請輸入分類名稱"
                          />
                        </div>
                        <div>
                          <Label htmlFor="slug">URL 標識符</Label>
                          <Input
                            id="slug"
                            value={formData.slug}
                            onChange={(e) => setFormData({...formData, slug: e.target.value})}
                            placeholder="自動生成或手動輸入"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 mt-4">
                        <Button onClick={handleSave} size="sm" className="flex items-center gap-2">
                          <Save className="h-4 w-4" />
                          儲存
                        </Button>
                        <Button onClick={handleCancel} variant="outline" size="sm" className="flex items-center gap-2">
                          <X className="h-4 w-4" />
                          取消
                        </Button>
                      </div>
                    </div>
                  )}

                  {categories.map((category) => (
                    <div key={category.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      {editingId === category.id ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`edit-name-${category.id}`}>分類名稱</Label>
                            <Input
                              id={`edit-name-${category.id}`}
                              value={formData.name}
                              onChange={(e) => setFormData({...formData, name: e.target.value})}
                            />
                          </div>
                          <div>
                            <Label htmlFor={`edit-slug-${category.id}`}>URL 標識符</Label>
                            <Input
                              id={`edit-slug-${category.id}`}
                              value={formData.slug}
                              onChange={(e) => setFormData({...formData, slug: e.target.value})}
                            />
                          </div>
                          <div className="md:col-span-2 flex gap-2">
                            <Button onClick={handleSave} size="sm" className="flex items-center gap-2">
                              <Save className="h-4 w-4" />
                              儲存
                            </Button>
                            <Button onClick={handleCancel} variant="outline" size="sm" className="flex items-center gap-2">
                              <X className="h-4 w-4" />
                              取消
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <Badge 
                              className={category.is_active 
                                ? "bg-green-100 text-green-800" 
                                : "bg-gray-100 text-gray-800"
                              }
                            >
                              {category.name}
                            </Badge>
                            <div>
                              <p className="font-medium">/{category.slug}</p>
                              <p className="text-sm text-gray-500">{category.service_count || 0} 個服務項目</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant={category.is_active ? "default" : "secondary"}
                              size="sm"
                              onClick={() => toggleStatus(category.id)}
                            >
                              {category.is_active ? "啟用中" : "已停用"}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(category)}
                              className="flex items-center gap-2"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(category.id)}
                              className="flex items-center gap-2 text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {categories.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      尚無分類資料，點擊「新增分類」開始建立
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CategoryManagement;