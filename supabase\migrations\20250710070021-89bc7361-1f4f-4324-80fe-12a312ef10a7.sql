-- 為測試商家添加服務分類和服務項目
INSERT INTO public.service_categories (shop_id, name, slug, sort_order) VALUES 
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '本月活動', 'monthly-specials', 1),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '美甲', 'nail-art', 2),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '美睫', 'eyelash', 3),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '美容', 'beauty', 4);

-- 獲取剛插入的分類ID，用於後續插入服務
WITH categories AS (
  SELECT id, name FROM public.service_categories 
  WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
)
INSERT INTO public.services (category_id, name, description, duration_minutes, price_min, price_max, sort_order) 
SELECT 
  categories.id,
  service_data.name,
  service_data.description,
  service_data.duration_minutes,
  service_data.price_min,
  service_data.price_max,
  service_data.sort_order
FROM categories,
(VALUES
  ('本月活動', '照顧媽咪頭皮大保養', '專業頭皮護理，舒緩壓力，給媽咪最好的呵護', 120, 1800, 1800, 1),
  ('本月活動', '靚眼美人改造計畫', '專業眼部護理與美化，讓雙眼更動人', 180, 2000, 2000, 2),
  ('美甲', '美甲-單純卸甲', '包含基礎保養的專業卸甲服務', 60, 1200, 1200, 3),
  ('美甲', '美甲-單色、法式、貓眼、璀璨款', '法式是經典的美甲造型，在指甲後緣處搭配簡約的線條與變化', 120, 1680, 1680, 4),
  ('美甲', '美甲單色/跳色', '簡約或活潑的單色跳色設計', 150, 1200, 1200, 5),
  ('美睫', '美睫-小清新款 200P', '自然清新的睫毛嫁接，200根假睫毛', 125, 1280, 1280, 6)
) AS service_data(category_name, name, description, duration_minutes, price_min, price_max, sort_order)
WHERE categories.name = service_data.category_name;

-- 添加服務提供者（美容師）
INSERT INTO public.service_providers (shop_id, name, title, is_active) VALUES 
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Anna', '專業美甲師', true),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Betty', '資深美睫師', true),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Cindy', '美容護理師', true);

-- 添加時間段設定
INSERT INTO public.time_slots (shop_id, start_time, duration_minutes) VALUES 
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '09:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '10:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '14:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '15:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '16:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '17:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '18:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '19:00', 60),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '20:00', 60);

-- 添加一些票券範例
INSERT INTO public.coupons (shop_id, name, code, type, value, min_amount, usage_limit, valid_from, valid_to, description) VALUES 
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '新客戶專屬優惠', 'NEWCUSTOMER200', 'fixed', 200, 1000, 100, '2024-01-01', '2024-12-31', '新客戶首次預約可享200元折扣'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '夏日美甲優惠', 'SUMMER15', 'percentage', 15, 800, 50, '2024-06-01', '2024-08-31', '美甲服務享85折優惠'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'VIP專屬折扣', 'VIP500', 'fixed', 500, 2000, 20, '2024-01-01', '2024-12-31', 'VIP會員專屬500元折扣券');

-- 為管理功能添加 RLS 政策，讓管理員可以管理服務分類和服務項目
CREATE POLICY "管理員可以插入服務分類" ON public.service_categories
  FOR INSERT WITH CHECK (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新服務分類" ON public.service_categories
  FOR UPDATE USING (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以刪除服務分類" ON public.service_categories
  FOR DELETE USING (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以查看所有服務分類" ON public.service_categories
  FOR SELECT USING (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以插入服務項目" ON public.services
  FOR INSERT WITH CHECK (EXISTS(
    SELECT 1 FROM service_categories sc 
    WHERE sc.id = category_id AND is_admin(auth.uid(), sc.shop_id)
  ));

CREATE POLICY "管理員可以更新服務項目" ON public.services
  FOR UPDATE USING (EXISTS(
    SELECT 1 FROM service_categories sc 
    WHERE sc.id = category_id AND is_admin(auth.uid(), sc.shop_id)
  ));

CREATE POLICY "管理員可以刪除服務項目" ON public.services
  FOR DELETE USING (EXISTS(
    SELECT 1 FROM service_categories sc 
    WHERE sc.id = category_id AND is_admin(auth.uid(), sc.shop_id)
  ));

CREATE POLICY "管理員可以查看所有服務項目" ON public.services
  FOR SELECT USING (EXISTS(
    SELECT 1 FROM service_categories sc 
    WHERE sc.id = category_id AND is_admin(auth.uid(), sc.shop_id)
  ));

CREATE POLICY "管理員可以插入服務提供者" ON public.service_providers
  FOR INSERT WITH CHECK (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以更新服務提供者" ON public.service_providers
  FOR UPDATE USING (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以刪除服務提供者" ON public.service_providers
  FOR DELETE USING (is_admin(auth.uid(), shop_id));

CREATE POLICY "管理員可以查看所有服務提供者" ON public.service_providers
  FOR SELECT USING (is_admin(auth.uid(), shop_id));