import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, RefreshCw, Home, Bug } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { 
  autoFixStateIssues, 
  logStateValidationReport,
  emergencyStateRecovery 
} from "@/utils/stateValidationDiagnostic";

interface StateErrorHandlerProps {
  receivedState?: string;
  savedState?: string | null;
  onRetry?: () => void;
}

export const StateErrorHandler = ({ 
  receivedState, 
  savedState, 
  onRetry 
}: StateErrorHandlerProps) => {
  const navigate = useNavigate();
  const [isFixing, setIsFixing] = useState(false);

  const handleAutoFix = async () => {
    setIsFixing(true);
    try {
      console.log("🔧 開始自動修復 state 問題...");
      
      // 嘗試自動修復
      const fixSuccess = autoFixStateIssues();
      
      if (fixSuccess) {
        // 如果有接收到的 state，嘗試緊急恢復
        if (receivedState && !savedState) {
          const recoverySuccess = emergencyStateRecovery(receivedState);
          if (recoverySuccess) {
            console.log("✅ 緊急恢復成功");
            if (onRetry) {
              onRetry();
            } else {
              window.location.reload();
            }
            return;
          }
        }
        
        console.log("✅ 自動修復完成，請重新登入");
        navigate("/login");
      } else {
        console.error("❌ 自動修復失敗");
        alert("自動修復失敗，請手動清除瀏覽器快取後重試");
      }
    } catch (error) {
      console.error("修復過程發生錯誤:", error);
      alert("修復過程發生錯誤，請聯繫技術支援");
    } finally {
      setIsFixing(false);
    }
  };

  const handleShowDiagnostic = () => {
    console.group("🔍 State 驗證問題診斷");
    console.log("接收到的 state:", receivedState);
    console.log("儲存的 state:", savedState);
    console.log("問題類型:", !savedState ? "localStorage 中沒有 state" : "state 不匹配");
    
    // 執行完整診斷
    logStateValidationReport();
    
    console.groupEnd();
    alert("詳細診斷報告已輸出到瀏覽器控制台，請按 F12 查看");
  };

  const handleClearAndRetry = () => {
    // 清除所有相關的 localStorage 項目
    const keysToRemove = Object.keys(localStorage).filter(key => 
      key.includes('line') || key.includes('auth') || key.includes('state')
    );
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log("🧹 已清除所有相關的 localStorage 項目");
    navigate("/login");
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-soft to-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="w-6 h-6 text-red-600" />
          </div>
          <CardTitle className="text-red-600">安全驗證失敗</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600 space-y-2">
            <p>
              <strong>問題說明：</strong>
              {!savedState ? 
                "瀏覽器中沒有找到登入狀態，可能是因為瀏覽器清除了資料或使用了隱私模式。" :
                "登入狀態驗證失敗，可能是因為多個分頁同時登入或頁面被重新整理。"
              }
            </p>
            
            <div className="bg-gray-50 p-3 rounded-lg text-xs">
              <div><strong>接收狀態：</strong> {receivedState?.substring(0, 12)}...</div>
              <div><strong>儲存狀態：</strong> {savedState || "無"}</div>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleAutoFix}
              disabled={isFixing}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isFixing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  修復中...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  🔧 自動修復並重試
                </>
              )}
            </Button>

            <Button
              onClick={handleClearAndRetry}
              variant="outline"
              className="w-full"
            >
              <Home className="w-4 h-4 mr-2" />
              清除資料並重新登入
            </Button>

            <Button
              onClick={handleShowDiagnostic}
              variant="ghost"
              className="w-full text-gray-600"
            >
              <Bug className="w-4 h-4 mr-2" />
              查看詳細診斷報告
            </Button>
          </div>

          <div className="text-xs text-gray-500 text-center space-y-1">
            <p>💡 <strong>建議解決方法：</strong></p>
            <ul className="text-left space-y-1">
              <li>• 點擊「自動修復」讓系統嘗試解決問題</li>
              <li>• 確保只在一個分頁進行登入</li>
              <li>• 避免在登入過程中重新整理頁面</li>
              <li>• 如果使用隱私模式，請改用一般模式</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
