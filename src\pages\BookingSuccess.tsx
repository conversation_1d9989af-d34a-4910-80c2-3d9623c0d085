import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  Calendar, 
  Clock, 
  User, 
  MessageCircle,
  Home,
  CalendarDays
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";

const BookingSuccess = () => {
  const navigate = useNavigate();

  // Mock booking data - in real app this would come from state/context
  const bookingData = {
    bookingId: "HK202407150001",
    staff: {
      name: "專業美甲師 Anna",
      avatar: "👩‍💼"
    },
    service: "美甲-單色、法式、貓眼、璀璨款",
    date: "2024/07/15 (週一)",
    time: "14:00",
    duration: "2小時",
    price: 1680,
    finalPrice: 1480, // After coupon discount
    couponUsed: "WELCOME200"
  };

  // Simulate webhook trigger in real app
  useEffect(() => {
    // This would trigger the n8n webhook for booking success notification
    console.log("Triggering webhook for booking success:", bookingData);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <div className="flex flex-col items-center justify-center min-h-screen p-4 space-y-8">
        {/* Success Icon */}
        <div className="w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
          <CheckCircle className="w-12 h-12 text-white" />
        </div>

        {/* Success Message */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-bold text-foreground">預約成功！</h1>
          <p className="text-muted-foreground">
            您的預約已確認，我們將透過 LINE 發送確認通知
          </p>
        </div>

        {/* Booking Details */}
        <Card className="w-full max-w-md p-6 card-shadow">
          <div className="space-y-4">
            {/* Booking ID */}
            <div className="text-center">
              <p className="text-sm text-muted-foreground">預約編號</p>
              <p className="font-mono text-lg font-semibold text-primary">
                {bookingData.bookingId}
              </p>
            </div>

            <div className="w-full h-px bg-border"></div>

            {/* Staff */}
            <div className="flex items-center space-x-3">
              <div className="text-2xl">{bookingData.staff.avatar}</div>
              <div>
                <p className="font-medium text-foreground">{bookingData.staff.name}</p>
                <p className="text-sm text-muted-foreground">服務人員</p>
              </div>
            </div>

            {/* Service */}
            <div>
              <p className="text-sm text-muted-foreground mb-1">服務項目</p>
              <p className="font-medium text-foreground">{bookingData.service}</p>
            </div>

            {/* Date & Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground mb-1 flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  預約日期
                </p>
                <p className="font-medium text-foreground">{bookingData.date}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1 flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  預約時間
                </p>
                <p className="font-medium text-foreground">
                  {bookingData.time} ({bookingData.duration})
                </p>
              </div>
            </div>

            {/* Price */}
            <div className="bg-primary-lighter p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">實付金額</span>
                <span className="text-2xl font-bold text-primary">
                  ${bookingData.finalPrice}
                </span>
              </div>
              {bookingData.couponUsed && (
                <div className="flex justify-between items-center mt-2">
                  <span className="text-sm text-muted-foreground">
                    已使用票券: {bookingData.couponUsed}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    省 ${bookingData.price - bookingData.finalPrice}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Notification Info */}
        <Card className="w-full max-w-md p-4 bg-blue-50 border-blue-200">
          <div className="flex items-start space-x-3">
            <MessageCircle className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-900">LINE 通知已發送</p>
              <p className="text-blue-700 mt-1">
                預約確認訊息已發送至您的 LINE，預約前一天也會提醒您！
              </p>
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="w-full max-w-md space-y-3">
          <Button 
            className="w-full h-12 beauty-gradient text-white rounded-full font-medium smooth-transition"
            onClick={() => navigate("/profile")}
          >
            <CalendarDays className="w-4 h-4 mr-2" />
            查看我的預約
          </Button>
          
          <Button 
            variant="outline"
            className="w-full h-12 rounded-full font-medium"
            onClick={() => navigate("/")}
          >
            <Home className="w-4 h-4 mr-2" />
            回到首頁
          </Button>
        </div>

        {/* Footer */}
        <div className="text-center space-y-1 mt-8">
          <p className="text-xs text-muted-foreground">感謝您選擇 HOTCAKE</p>
          <p className="text-xs text-muted-foreground">Powered by 夯客 v3.11.0</p>
        </div>
      </div>
    </div>
  );
};

export default BookingSuccess;