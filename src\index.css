@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Light pink theme for beauty booking app */
    --background: 355 100% 98%;
    --foreground: 340 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 340 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 340 20% 15%;

    /* Primary pink color scheme */
    --primary: 340 75% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 340 60% 70%;
    --primary-lighter: 340 40% 90%;

    /* Secondary colors */
    --secondary: 340 30% 95%;
    --secondary-foreground: 340 20% 15%;

    --muted: 340 20% 96%;
    --muted-foreground: 340 10% 45%;

    --accent: 340 40% 92%;
    --accent-foreground: 340 20% 15%;

    /* Status colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 140 50% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 45 90% 55%;
    --warning-foreground: 0 0% 100%;

    /* UI elements */
    --border: 340 20% 88%;
    --input: 340 20% 88%;
    --ring: 340 75% 55%;

    --radius: 0.75rem;

    /* Beauty app specific colors */
    --pink-soft: 340 60% 95%;
    --pink-warm: 320 45% 85%;
    --rose-gold: 15 65% 75%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-soft: linear-gradient(180deg, hsl(var(--pink-soft)), hsl(var(--background)));

    /* Shadows */
    --shadow-soft: 0 4px 20px -4px hsl(var(--primary) / 0.15);
    --shadow-card: 0 2px 10px -2px hsl(var(--primary) / 0.1);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 340 30% 8%;
    --foreground: 340 10% 90%;

    --card: 340 25% 12%;
    --card-foreground: 340 10% 90%;

    --popover: 340 25% 12%;
    --popover-foreground: 340 10% 90%;

    --primary: 340 65% 65%;
    --primary-foreground: 340 30% 8%;

    --secondary: 340 20% 18%;
    --secondary-foreground: 340 10% 90%;

    --muted: 340 15% 20%;
    --muted-foreground: 340 8% 65%;

    --accent: 340 20% 18%;
    --accent-foreground: 340 10% 90%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 340 15% 20%;
    --input: 340 15% 20%;
    --ring: 340 65% 65%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .beauty-gradient {
    background: var(--gradient-primary);
  }
  
  .soft-shadow {
    box-shadow: var(--shadow-soft);
  }
  
  .card-shadow {
    box-shadow: var(--shadow-card);
  }
  
  .smooth-transition {
    transition: var(--transition-smooth);
  }
}