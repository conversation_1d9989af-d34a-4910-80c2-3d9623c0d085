# LINE Login 錯誤情況分析指南

## 🚨 「無法獲取 LINE 授權，請重新登入」錯誤

這個錯誤發生在 LINE Login 的 token 交換階段，以下是可能的原因和解決方案：

### 📋 常見原因分析

#### 1. **LINE 設定問題** (最常見)
- **Channel ID 錯誤或未設定**
  - 症狀：系統無法識別您的 LINE 應用
  - 解決：檢查 Channel ID 是否正確複製自 LINE Developers Console

- **Channel Secret 錯誤或未設定**
  - 症狀：LINE API 拒絕 token 交換請求
  - 解決：確認 Channel Secret 正確且未過期

- **Redirect URI 不匹配**
  - 症狀：LINE 回傳 redirect_uri 錯誤
  - 解決：確保系統設定的 URI 與 LINE Console 中註冊的完全一致

#### 2. **Authorization Code 問題**
- **Code 已過期**
  - 症狀：LINE 回傳 invalid_grant 錯誤
  - 原因：Authorization code 只有 10 分鐘有效期
  - 解決：重新進行登入流程

- **Code 已被使用**
  - 症狀：LINE 回傳 invalid_grant 錯誤
  - 原因：每個 code 只能使用一次
  - 解決：清除瀏覽器快取，重新登入

#### 3. **網路或服務問題**
- **Supabase Edge Function 無法訪問**
  - 症狀：Function 調用失敗
  - 解決：檢查 Supabase 服務狀態

- **LINE API 服務暫時不可用**
  - 症狀：LINE API 回傳 5xx 錯誤
  - 解決：稍後重試

### 🔧 診斷步驟

#### 步驟 1：使用診斷工具
1. 前往 [診斷頁面](https://line-beauty-appoint.lovable.app/admin/line-test)
2. 查看「詳細診斷結果」區塊
3. 按照「修復建議」進行操作

#### 步驟 2：檢查瀏覽器控制台
1. 按 F12 開啟開發者工具
2. 切換到 Console 頁籤
3. 查看是否有錯誤訊息
4. 特別注意 "Failed to exchange token" 相關訊息

#### 步驟 3：驗證 LINE 設定
1. 前往 [LINE Settings](https://line-beauty-appoint.lovable.app/admin/line-settings)
2. 確認所有欄位都已正確填入
3. 點擊「測試 LINE Login」按鈕
4. 檢查測試結果

### 🛠️ 解決方案

#### 方案 1：重新設定 LINE 參數
```
1. 前往 LINE Developers Console
2. 複製正確的 Channel ID 和 Channel Secret
3. 在系統中重新填入這些參數
4. 點擊「自動設定」來設定正確的 Redirect URI
5. 儲存設定並重新測試
```

#### 方案 2：清除瀏覽器狀態
```
1. 清除瀏覽器快取和 Cookie
2. 清除 localStorage (或按 F12 → Application → Local Storage → Clear All)
3. 重新開始登入流程
```

#### 方案 3：檢查 LINE Console 設定
```
1. 確認 Channel 狀態為 "Published"
2. 確認 Callback URL 完全匹配：
   https://line-beauty-appoint.lovable.app/auth/line/callback
3. 確認 Channel 類型為 "LINE Login"
```

### 📊 錯誤代碼對照表

| 錯誤訊息 | 可能原因 | 解決方法 |
|---------|---------|---------|
| `invalid_grant` | Authorization code 無效/過期 | 重新登入 |
| `invalid_client` | Channel ID 錯誤 | 檢查 Channel ID |
| `invalid_request` | 請求參數錯誤 | 檢查所有設定參數 |
| `unauthorized_client` | Channel Secret 錯誤 | 檢查 Channel Secret |
| `redirect_uri_mismatch` | Redirect URI 不匹配 | 檢查 Callback URL 設定 |

### 🔍 進階除錯

#### 檢查 Edge Function 日誌
如果您有 Supabase 專案的管理權限：
1. 前往 Supabase Dashboard
2. 查看 Edge Functions 的執行日誌
3. 尋找 `line-login-token` function 的錯誤訊息

#### 手動測試 LINE API
使用 Postman 或類似工具測試：
```bash
POST https://api.line.me/oauth2/v2.1/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=YOUR_CODE&
redirect_uri=https://line-beauty-appoint.lovable.app/auth/line/callback&
client_id=YOUR_CHANNEL_ID&
client_secret=YOUR_CHANNEL_SECRET
```

### 🆘 仍然無法解決？

1. **收集錯誤資訊**：
   - 瀏覽器控制台的完整錯誤訊息
   - 診斷頁面的結果截圖
   - LINE Developers Console 的設定截圖

2. **檢查系統狀態**：
   - [Supabase Status](https://status.supabase.com/)
   - [LINE Developers Status](https://developers.line.biz/)

3. **聯繫技術支援**：
   - 提供上述收集的錯誤資訊
   - 說明已嘗試的解決步驟

### 💡 預防措施

1. **定期檢查設定**：每月檢查一次 LINE 設定是否正確
2. **監控錯誤**：設定錯誤監控來及時發現問題
3. **備份設定**：記錄所有 LINE 設定參數
4. **測試流程**：定期測試完整的登入流程

---

**記住**：大多數 LINE Login 問題都是由於設定不正確造成的。使用診斷工具可以快速識別和解決大部分問題。
