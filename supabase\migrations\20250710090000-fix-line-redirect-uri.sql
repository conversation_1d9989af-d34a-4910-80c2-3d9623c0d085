-- Fix LINE redirect URI to match Lovable production environment
-- This migration ensures the redirect_uri is correctly set for the production environment

-- Update the redirect URI for the test shop to match Lovable deployment
UPDATE public.line_settings
SET redirect_uri = 'https://line-beauty-appoint.lovable.app/auth/line/callback'
WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

-- If no record exists, insert a default one
INSERT INTO public.line_settings (shop_id, channel_id, channel_secret, redirect_uri)
VALUES (
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '',
  '',
  'https://line-beauty-appoint.lovable.app/auth/line/callback'
) ON CONFLICT (shop_id) DO UPDATE SET
  redirect_uri = EXCLUDED.redirect_uri;

-- Add a helpful comment
COMMENT ON COLUMN public.line_settings.redirect_uri IS 'The callback URL for LINE Login OAuth flow. Production URL: https://line-beauty-appoint.lovable.app/auth/line/callback';
